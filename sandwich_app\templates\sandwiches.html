{% extends "base.html" %}

{% block title %}Sandwiches - Sandwich Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="bi bi-card-list text-primary"></i> Sandwich Management
                </h2>
                <a href="{{ url_for('add_sandwich') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> Add New Sandwich
                </a>
            </div>

            {% if sandwiches %}
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Ingredients</th>
                                    <th>Price</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sandwich in sandwiches %}
                                <tr>
                                    <td>
                                        <strong>{{ sandwich.name }}</strong>
                                    </td>
                                    <td>{{ sandwich.description or '-' }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ sandwich.ingredients|length }} ingredients</span>
                                    </td>
                                    <td>
                                        <span class="text-success fw-bold">${{ "%.2f"|format(sandwich.calculate_cost()) }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('edit_sandwich', sandwich_id=loop.index0) }}" 
                                               class="btn btn-outline-primary">
                                                <i class="bi bi-pencil"></i> Edit
                                            </a>
                                            <button class="btn btn-outline-danger" 
                                                    onclick="confirmDelete('{{ sandwich.name }}', '{{ loop.index0 }}')">
                                                <i class="bi bi-trash"></i> Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="bi bi-card-list text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">No Sandwiches Found</h4>
                    <p class="text-muted">Get started by adding your first sandwich type.</p>
                    <a href="{{ url_for('add_sandwich') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add First Sandwich
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function confirmDelete(name, id) {
    if (confirm(`Are you sure you want to delete "${name}"?`)) {
        // Add delete functionality here
        window.location.href = `/sandwiches/delete/${id}`;
    }
}
</script>
{% endblock %}
