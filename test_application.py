#!/usr/bin/env python3
"""
Comprehensive test script for the Sandwich Management System.
Tests all major functionality and CRUD operations.
"""

import requests
import time
import sys

BASE_URL = "http://127.0.0.1:5000"

def test_endpoint(endpoint, expected_status=200, method='GET', data=None):
    """Test a single endpoint and return success status."""
    try:
        url = f"{BASE_URL}{endpoint}"
        if method == 'GET':
            response = requests.get(url, timeout=5)
        elif method == 'POST':
            response = requests.post(url, data=data, timeout=5)
        
        success = response.status_code == expected_status
        print(f"{'✓' if success else '✗'} {method} {endpoint} - Status: {response.status_code}")
        return success
    except Exception as e:
        print(f"✗ {method} {endpoint} - Error: {str(e)}")
        return False

def main():
    """Run comprehensive tests of the application."""
    print("🧪 Testing Sandwich Management System")
    print("=" * 50)
    
    # Test basic connectivity
    print("\n📡 Testing Basic Connectivity:")
    if not test_endpoint("/"):
        print("❌ Application is not running or not accessible!")
        sys.exit(1)
    
    # Test main navigation pages
    print("\n🧭 Testing Navigation Pages:")
    navigation_tests = [
        "/",
        "/sandwiches",
        "/ingredients", 
        "/suppliers",
        "/shops",
        "/daily-plan",
        "/production",
        "/deliveries",
        "/returns",
        "/reports"
    ]
    
    nav_results = []
    for endpoint in navigation_tests:
        nav_results.append(test_endpoint(endpoint))
    
    # Test add forms
    print("\n📝 Testing Add Forms:")
    form_tests = [
        "/sandwiches/add",
        "/ingredients/add",
        "/suppliers/add", 
        "/shops/add"
    ]
    
    form_results = []
    for endpoint in form_tests:
        form_results.append(test_endpoint(endpoint))
    
    # Test API endpoints
    print("\n🔌 Testing API Endpoints:")
    api_tests = [
        "/api/charts/sales_trend",
        "/api/charts/popularity",
        "/api/charts/shops"
    ]
    
    api_results = []
    for endpoint in api_tests:
        api_results.append(test_endpoint(endpoint))
    
    # Test report endpoints
    print("\n📊 Testing Report Endpoints:")
    report_tests = [
        "/reports/daily-summary",
        "/reports/shop-invoice"
    ]
    
    report_results = []
    for endpoint in report_tests:
        report_results.append(test_endpoint(endpoint))
    
    # Test export endpoints
    print("\n💾 Testing Export Endpoints:")
    export_tests = [
        "/export?type=daily_plans",
        "/export?type=deliveries", 
        "/export?type=financial"
    ]
    
    export_results = []
    for endpoint in export_tests:
        export_results.append(test_endpoint(endpoint))
    
    # Test admin endpoints
    print("\n⚙️ Testing Admin Endpoints:")
    admin_tests = [
        "/admin/save-data",
        "/admin/backup-data"
    ]
    
    admin_results = []
    for endpoint in admin_tests:
        admin_results.append(test_endpoint(endpoint, expected_status=302))  # Redirects
    
    # Calculate results
    print("\n" + "=" * 50)
    print("📈 TEST RESULTS SUMMARY:")
    print("=" * 50)
    
    total_tests = 0
    passed_tests = 0
    
    categories = [
        ("Navigation Pages", nav_results),
        ("Add Forms", form_results), 
        ("API Endpoints", api_results),
        ("Report Endpoints", report_results),
        ("Export Endpoints", export_results),
        ("Admin Endpoints", admin_results)
    ]
    
    for category_name, results in categories:
        category_passed = sum(results)
        category_total = len(results)
        total_tests += category_total
        passed_tests += category_passed
        
        percentage = (category_passed / category_total * 100) if category_total > 0 else 0
        status = "✅" if percentage == 100 else "⚠️" if percentage >= 80 else "❌"
        
        print(f"{status} {category_name}: {category_passed}/{category_total} ({percentage:.1f}%)")
    
    overall_percentage = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    overall_status = "✅" if overall_percentage == 100 else "⚠️" if overall_percentage >= 80 else "❌"
    
    print("-" * 50)
    print(f"{overall_status} OVERALL: {passed_tests}/{total_tests} ({overall_percentage:.1f}%)")
    
    if overall_percentage == 100:
        print("\n🎉 ALL TESTS PASSED! The application is fully functional.")
    elif overall_percentage >= 80:
        print("\n⚠️ Most tests passed. Some features may need attention.")
    else:
        print("\n❌ Many tests failed. The application needs significant fixes.")
    
    # Test specific functionality
    print("\n🔧 Testing Specific Functionality:")
    
    # Test data persistence
    print("Testing data persistence...")
    save_result = test_endpoint("/admin/save-data", expected_status=302)
    backup_result = test_endpoint("/admin/backup-data", expected_status=302)
    
    if save_result and backup_result:
        print("✅ Data persistence and backup functionality working")
    else:
        print("❌ Data persistence or backup functionality issues")
    
    print("\n" + "=" * 50)
    print("🏁 TESTING COMPLETE")
    print("=" * 50)
    
    return overall_percentage >= 80

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
