{% extends "base.html" %}

{% block title %}Edit Ingredient - Sandwich Management System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3><i class="bi bi-pencil"></i> Edit Ingredient</h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">Ingredient Name *</label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ ingredient.name }}" required>
                        <div class="form-text">Enter the name of the ingredient (e.g., "Bread", "Ham", "Cheese").</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="unit" class="form-label">Unit of Measurement *</label>
                        <input type="text" class="form-control" id="unit" name="unit" value="{{ ingredient.unit }}" required placeholder="e.g., kg, loaf, jar, head">
                        <div class="form-text">How this ingredient is measured (kg, grams, pieces, etc.).</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="price" class="form-label">Price per Unit ($) *</label>
                        <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" value="{{ ingredient.price_per_unit }}" required>
                        <div class="form-text">Cost per unit from the supplier.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="supplier_id" class="form-label">Supplier *</label>
                        <select class="form-select" id="supplier_id" name="supplier_id" required>
                            <option value="">Select a supplier...</option>
                            {% for supplier in suppliers %}
                                <option value="{{ loop.index0 }}" {% if supplier == ingredient.supplier %}selected{% endif %}>{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Choose which supplier provides this ingredient.</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('ingredients') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Ingredients
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Update Ingredient
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
