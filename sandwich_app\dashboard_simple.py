from datetime import datetime, timedelta
import io
import base64

class Dashboard:
    def __init__(self, app):
        self.app = app
        
    def generate_summary_stats(self, days=30):
        """Generate key business metrics for the dashboard"""
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        stats = {
            "period": f"{start_date} to {end_date}",
            "total_sandwiches_produced": 0,
            "total_sandwiches_sold": 0,
            "return_rate": 0,
            "revenue": 0,
            "costs": 0,
            "profit": 0,
            "top_sandwich": {"name": "None", "sold": 0},
            "top_shop": {"name": "None", "sold": 0}
        }
        
        # Collect data from daily plans
        sandwich_sales = {s.name: 0 for s in self.app.sandwich_types}
        shop_sales = {s.name: 0 for s in self.app.shops}
        
        for plan_date, plan in self.app.daily_plans.items():
            if start_date <= plan_date <= end_date:
                # Aggregate production and sales
                for sandwich in self.app.sandwich_types:
                    produced = plan.produced_quantities.get(sandwich, 0)
                    sold = plan.get_total_sold(sandwich)
                    
                    stats["total_sandwiches_produced"] += produced
                    stats["total_sandwiches_sold"] += sold
                    sandwich_sales[sandwich.name] += sold
                    
                    # Track shop performance
                    for shop in self.app.shops:
                        shop_sold = plan.get_sold_by_shop(shop, sandwich)
                        shop_sales[shop.name] += shop_sold
                
                # Add financial data
                stats["revenue"] += plan.calculate_daily_revenue()
                stats["costs"] += plan.calculate_daily_costs()
        
        stats["profit"] = stats["revenue"] - stats["costs"]
        
        # Calculate return rate
        if stats["total_sandwiches_produced"] > 0:
            stats["return_rate"] = (stats["total_sandwiches_produced"] - stats["total_sandwiches_sold"]) / stats["total_sandwiches_produced"] * 100
        
        # Find top performers
        if sandwich_sales and any(sandwich_sales.values()):
            top_sandwich_name = max(sandwich_sales, key=sandwich_sales.get)
            stats["top_sandwich"]["name"] = top_sandwich_name
            stats["top_sandwich"]["sold"] = sandwich_sales[top_sandwich_name]
            
        if shop_sales and any(shop_sales.values()):
            top_shop_name = max(shop_sales, key=shop_sales.get)
            stats["top_shop"]["name"] = top_shop_name
            stats["top_shop"]["sold"] = shop_sales[top_shop_name]
            
        return stats
    
    def plot_daily_sales_trend(self, days=30):
        """Generate sales trend data (placeholder for charts)"""
        return self._create_placeholder_chart("Sales Trend Chart")
    
    def plot_sandwich_popularity(self):
        """Generate sandwich popularity data (placeholder for charts)"""
        return self._create_placeholder_chart("Sandwich Popularity Chart")
    
    def plot_shop_performance(self):
        """Generate shop performance data (placeholder for charts)"""
        return self._create_placeholder_chart("Shop Performance Chart")
    
    def _create_placeholder_chart(self, title):
        """Create a placeholder chart image"""
        # Create a simple SVG placeholder
        svg_content = f'''
        <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
            <rect width="400" height="300" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
            <text x="200" y="150" text-anchor="middle" font-family="Arial" font-size="16" fill="#6c757d">
                {title}
            </text>
            <text x="200" y="180" text-anchor="middle" font-family="Arial" font-size="12" fill="#6c757d">
                (Chart visualization requires matplotlib)
            </text>
        </svg>
        '''
        return svg_content
    
    def fig_to_base64(self, fig):
        """Convert figure to base64 (placeholder)"""
        if isinstance(fig, str):  # SVG content
            return base64.b64encode(fig.encode('utf-8')).decode('utf-8')
        return ""
