from flask import Flask, render_template, request, jsonify
from datetime import datetime, timedelta
import json
import io
import base64
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend

class WebDashboard:
    def __init__(self, app):
        self.sandwich_app = app
        self.dashboard = Dashboard(app)
        self.flask_app = Flask(__name__)
        self.setup_routes()
        
    def setup_routes(self):
        @self.flask_app.route('/')
        def home():
            stats = self.dashboard.generate_summary_stats()
            return render_template('dashboard.html', stats=stats)
        
        @self.flask_app.route('/api/summary')
        def api_summary():
            days = request.args.get('days', default=30, type=int)
            return jsonify(self.dashboard.generate_summary_stats(days))
        
        @self.flask_app.route('/api/charts/sales_trend')
        def api_sales_trend():
            days = request.args.get('days', default=30, type=int)
            fig = self.dashboard.plot_daily_sales_trend(days)
            img = self.fig_to_base64(fig)
            return jsonify({'image': img})
        
        @self.flask_app.route('/api/charts/popularity')
        def api_popularity():
            fig = self.dashboard.plot_sandwich_popularity()
            img = self.fig_to_base64(fig)
            return jsonify({'image': img})
        
        @self.flask_app.route('/api/charts/shops')
        def api_shops():
            fig = self.dashboard.plot_shop_performance()
            img = self.fig_to_base64(fig)
            return jsonify({'image': img})
    
    def fig_to_base64(self, fig):
        """Convert matplotlib figure to base64 string for web display"""
        buf = io.BytesIO()
        fig.savefig(buf, format='png')
        buf.seek(0)
        img_str = base64.b64encode(buf.read()).decode('utf-8')
        return img_str
    
    def run(self, debug=True, port=5000):
        self.flask_app.run(debug=debug, port=port)