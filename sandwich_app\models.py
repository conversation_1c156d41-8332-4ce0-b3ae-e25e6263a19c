from datetime import date

class Supplier:
    def __init__(self, name, contact_info=""):
        self.name = name
        self.contact_info = contact_info
        
    def __repr__(self):
        return f"Supplier({self.name})"

class Ingredient:
    def __init__(self, name, unit, supplier, price_per_unit):
        self.name = name
        self.unit = unit  # e.g., "kg", "piece"
        self.supplier = supplier
        self.price_per_unit = price_per_unit
        
    def __repr__(self):
        return f"Ingredient({self.name}, {self.price_per_unit}/{self.unit})"

class SandwichType:
    def __init__(self, name, description=""):
        self.name = name
        self.description = description
        self.recipe = {}  # ingredient: quantity_per_sandwich
        self.selling_price = 0
        
    def add_ingredient(self, ingredient, quantity):
        self.recipe[ingredient] = quantity
        
    def set_price(self, price):
        self.selling_price = price
        
    def calculate_cost(self):
        total_cost = 0
        for ingredient, quantity in self.recipe.items():
            total_cost += ingredient.price_per_unit * quantity
        return total_cost
        
    def calculate_profit_margin(self):
        cost = self.calculate_cost()
        if cost == 0 or self.selling_price == 0:
            return 0
        return (self.selling_price - cost) / self.selling_price * 100
        
    def __repr__(self):
        return f"SandwichType({self.name})"

class Shop:
    def __init__(self, name, location=""):
        self.name = name
        self.location = location
        
    def __repr__(self):
        return f"Shop({self.name})"

class DailyPlan:
    def __init__(self, plan_date=None):
        self.date = plan_date if plan_date else date.today()
        self.planned_quantities = {}  # sandwich_type: planned_count
        self.produced_quantities = {}  # sandwich_type: actual_produced
        self.shop_deliveries = {}  # shop: {sandwich_type: delivered_count}
        self.shop_returns = {}  # shop: {sandwich_type: returned_count}
        
    def plan_quantity(self, sandwich_type, quantity):
        self.planned_quantities[sandwich_type] = quantity
        
    def record_production(self, sandwich_type, quantity):
        self.produced_quantities[sandwich_type] = quantity
        
    def record_delivery(self, shop, sandwich_type, quantity):
        if shop not in self.shop_deliveries:
            self.shop_deliveries[shop] = {}
        self.shop_deliveries[shop][sandwich_type] = quantity
        
    def record_returns(self, shop, sandwich_type, quantity):
        if shop not in self.shop_returns:
            self.shop_returns[shop] = {}
        self.shop_returns[shop][sandwich_type] = quantity
        
    def get_sold_by_shop(self, shop, sandwich_type):
        delivered = self.shop_deliveries.get(shop, {}).get(sandwich_type, 0)
        returned = self.shop_returns.get(shop, {}).get(sandwich_type, 0)
        return delivered - returned
        
    def get_total_sold(self, sandwich_type):
        total = 0
        for shop in self.shop_deliveries:
            total += self.get_sold_by_shop(shop, sandwich_type)
        return total
        
    def calculate_daily_revenue(self):
        revenue = 0
        for sandwich_type in set().union(*([d.keys() for d in self.shop_deliveries.values()] if self.shop_deliveries else [set()])):
            sold = self.get_total_sold(sandwich_type)
            revenue += sold * sandwich_type.selling_price
        return revenue
        
    def calculate_daily_costs(self):
        costs = 0
        for sandwich_type, quantity in self.produced_quantities.items():
            costs += sandwich_type.calculate_cost() * quantity
        return costs
        
    def calculate_daily_profit(self):
        return self.calculate_daily_revenue() - self.calculate_daily_costs()