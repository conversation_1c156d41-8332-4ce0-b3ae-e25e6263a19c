from datetime import datetime, timedelta
import calendar
import io
import base64

class Dashboard:
    def __init__(self, app):
        self.app = app
        
    def generate_summary_stats(self, days=30):
        """Generate key business metrics for the dashboard"""
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        stats = {
            "period": f"{start_date} to {end_date}",
            "total_sandwiches_produced": 0,
            "total_sandwiches_sold": 0,
            "return_rate": 0,
            "revenue": 0,
            "costs": 0,
            "profit": 0,
            "top_sandwich": {"name": "", "sold": 0},
            "top_shop": {"name": "", "sold": 0}
        }
        
        # Collect data from daily plans
        sandwich_sales = {s: 0 for s in self.app.sandwich_types}
        shop_sales = {s: 0 for s in self.app.shops}
        
        for plan_date, plan in self.app.daily_plans.items():
            if start_date <= plan_date <= end_date:
                # Aggregate production and sales
                for sandwich in self.app.sandwich_types:
                    produced = plan.produced_quantities.get(sandwich, 0)
                    sold = plan.get_total_sold(sandwich)
                    
                    stats["total_sandwiches_produced"] += produced
                    stats["total_sandwiches_sold"] += sold
                    sandwich_sales[sandwich] += sold
                    
                    # Track shop performance
                    for shop in self.app.shops:
                        shop_sold = plan.get_sold_by_shop(shop, sandwich)
                        shop_sales[shop] += shop_sold
                
                # Add financial data
                stats["revenue"] += plan.calculate_daily_revenue()
                stats["costs"] += plan.calculate_daily_costs()
        
        # Calculate derived metrics
        stats["profit"] = stats["revenue"] - stats["costs"]
        if stats["total_sandwiches_produced"] > 0:
            stats["return_rate"] = (stats["total_sandwiches_produced"] - stats["total_sandwiches_sold"]) / stats["total_sandwiches_produced"] * 100
        
        # Find top performers
        if sandwich_sales:
            stats["top_sandwich"]["name"] = max(sandwich_sales, key=sandwich_sales.get).name
            stats["top_sandwich"]["sold"] = sandwich_sales[max(sandwich_sales, key=sandwich_sales.get)]
        
        if shop_sales:
            stats["top_shop"]["name"] = max(shop_sales, key=shop_sales.get).name
            stats["top_shop"]["sold"] = shop_sales[max(shop_sales, key=shop_sales.get)]
            
        return stats
    
    def plot_daily_sales_trend(self, days=30):
        """Generate a line chart of daily sales"""
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        dates = []
        sales = []
        profits = []
        
        for i in range(days):
            current_date = start_date + timedelta(days=i)
            plan = self.app.daily_plans.get(current_date)
            
            dates.append(current_date)
            if plan:
                total_sold = sum(plan.get_total_sold(s) for s in self.app.sandwich_types)
                sales.append(total_sold)
                profits.append(plan.calculate_daily_profit())
            else:
                sales.append(0)
                profits.append(0)
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        ax1.plot(dates, sales, marker='o', linestyle='-', color='blue')
        ax1.set_title('Daily Sandwich Sales')
        ax1.set_ylabel('Number of Sandwiches')
        ax1.grid(True)
        
        ax2.plot(dates, profits, marker='o', linestyle='-', color='green')
        ax2.set_title('Daily Profit')
        ax2.set_ylabel('Profit ($)')
        ax2.grid(True)
        
        plt.tight_layout()
        return fig
    
    def plot_sandwich_popularity(self):
        """Generate a pie chart showing sandwich popularity"""
        sandwich_sales = {s.name: 0 for s in self.app.sandwich_types}
        
        for plan in self.app.daily_plans.values():
            for sandwich in self.app.sandwich_types:
                sandwich_sales[sandwich.name] += plan.get_total_sold(sandwich)
        
        labels = list(sandwich_sales.keys())
        sizes = list(sandwich_sales.values())
        
        fig, ax = plt.subplots(figsize=(10, 7))
        ax.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
        ax.axis('equal')
        ax.set_title('Sandwich Type Popularity')
        
        return fig
    
    def plot_shop_performance(self):
        """Generate a bar chart of shop performance"""
        shop_sales = {s.name: 0 for s in self.app.shops}
        shop_returns = {s.name: 0 for s in self.app.shops}
        
        for plan in self.app.daily_plans.values():
            for shop in self.app.shops:
                for sandwich in self.app.sandwich_types:
                    delivered = plan.shop_deliveries.get(shop, {}).get(sandwich, 0)
                    returned = plan.shop_returns.get(shop, {}).get(sandwich, 0)
                    shop_sales[shop.name] += delivered - returned
                    shop_returns[shop.name] += returned
        
        shops = list(shop_sales.keys())
        sales = list(shop_sales.values())
        returns = list(shop_returns.values())
        
        fig, ax = plt.subplots(figsize=(12, 6))
        
        x = range(len(shops))
        width = 0.35
        
        ax.bar([i - width/2 for i in x], sales, width, label='Sales')
        ax.bar([i + width/2 for i in x], returns, width, label='Returns')
        
        ax.set_ylabel('Number of Sandwiches')
        ax.set_title('Shop Performance')
        ax.set_xticks(x)
        ax.set_xticklabels(shops)
        ax.legend()
        
        return fig
    
    def generate_dashboard(self):
        """Generate a complete dashboard with all visualizations"""
        stats = self.generate_summary_stats()
        
        print("=" * 50)
        print("SANDWICH BUSINESS DASHBOARD")
        print("=" * 50)
        print(f"Period: {stats['period']}")
        print(f"Total Produced: {stats['total_sandwiches_produced']} sandwiches")
        print(f"Total Sold: {stats['total_sandwiches_sold']} sandwiches")
        print(f"Return Rate: {stats['return_rate']:.1f}%")
        print(f"Revenue: ${stats['revenue']:.2f}")
        print(f"Costs: ${stats['costs']:.2f}")
        print(f"Profit: ${stats['profit']:.2f}")
        print(f"Profit Margin: {(stats['profit']/stats['revenue']*100 if stats['revenue'] else 0):.1f}%")
        print(f"Top Sandwich: {stats['top_sandwich']['name']} ({stats['top_sandwich']['sold']} sold)")
        print(f"Top Shop: {stats['top_shop']['name']} ({stats['top_shop']['sold']} sold)")
        print("=" * 50)
        
        # Generate and save all plots
        sales_trend = self.plot_daily_sales_trend()
        sales_trend.savefig('dashboard_sales_trend.png')
        
        popularity = self.plot_sandwich_popularity()
        popularity.savefig('dashboard_sandwich_popularity.png')
        
        shop_perf = self.plot_shop_performance()
        shop_perf.savefig('dashboard_shop_performance.png')
        
        print("Dashboard visualizations saved as PNG files.")
        return stats