{% extends "base.html" %}

{% block title %}Daily Plan - Sandwich Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-calendar-check"></i> Daily Plan</h1>
    <div>
        <input type="date" class="form-control" id="date-picker" 
               value="{{ plan_date.strftime('%Y-%m-%d') }}" 
               onchange="window.location.href='{{ url_for('daily_plan') }}?date=' + this.value">
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>Plan for {{ plan_date.strftime('%B %d, %Y') }}</h5>
            </div>
            <div class="card-body">
                {% if sandwiches %}
                    <form method="POST">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Sandwich</th>
                                        <th>Current Plan</th>
                                        <th>New Quantity</th>
                                        <th>Cost per Unit</th>
                                        <th>Total Cost</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for sandwich in sandwiches %}
                                        {% set current_plan = plan.planned_quantities.get(sandwich, 0) %}
                                        <tr>
                                            <td>
                                                <strong>{{ sandwich.name }}</strong><br>
                                                <small class="text-muted">${{ "%.2f"|format(sandwich.selling_price) }}</small>
                                            </td>
                                            <td>{{ current_plan }}</td>
                                            <td>
                                                <input type="number" class="form-control" 
                                                       name="planned_{{ loop.index0 }}" 
                                                       value="{{ current_plan }}" 
                                                       min="0" step="1">
                                            </td>
                                            <td>${{ "%.2f"|format(sandwich.calculate_cost()) }}</td>
                                            <td class="cost-cell" data-cost="{{ sandwich.calculate_cost() }}">
                                                ${{ "%.2f"|format(current_plan * sandwich.calculate_cost()) }}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr class="table-info">
                                        <th colspan="4">Total Planned Cost:</th>
                                        <th id="total-cost">
                                            ${{ "%.2f"|format(plan.calculate_daily_costs()) }}
                                        </th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Plan
                            </button>
                        </div>
                    </form>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-inbox display-4 text-muted"></i>
                        <h4 class="text-muted">No sandwiches available</h4>
                        <p class="text-muted">Add sandwich types first to create a daily plan.</p>
                        <a href="{{ url_for('add_sandwich') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Add Sandwich
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6>Quick Stats</h6>
            </div>
            <div class="card-body">
                <div class="metric-card bg-light">
                    <div class="metric-value">{{ plan.planned_quantities.values() | sum }}</div>
                    <div class="metric-label">Total Planned</div>
                </div>
                <div class="metric-card bg-light">
                    <div class="metric-value">${{ "%.2f"|format(plan.calculate_daily_costs()) }}</div>
                    <div class="metric-label">Estimated Cost</div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6>Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('production', date=plan_date.strftime('%Y-%m-%d')) }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-gear"></i> Go to Production
                    </a>
                    <a href="{{ url_for('deliveries', date=plan_date.strftime('%Y-%m-%d')) }}" class="btn btn-outline-success btn-sm">
                        <i class="bi bi-truck"></i> Manage Deliveries
                    </a>
                    <a href="{{ url_for('daily_summary_report', date=plan_date.strftime('%Y-%m-%d')) }}" class="btn btn-outline-info btn-sm">
                        <i class="bi bi-file-text"></i> View Summary
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Update cost calculations when quantities change
    document.addEventListener('DOMContentLoaded', function() {
        const quantityInputs = document.querySelectorAll('input[name^="planned_"]');
        
        quantityInputs.forEach(input => {
            input.addEventListener('input', updateCosts);
        });
        
        function updateCosts() {
            let totalCost = 0;
            
            quantityInputs.forEach((input, index) => {
                const quantity = parseFloat(input.value) || 0;
                const costCell = document.querySelectorAll('.cost-cell')[index];
                const unitCost = parseFloat(costCell.dataset.cost);
                const lineCost = quantity * unitCost;
                
                costCell.textContent = '$' + lineCost.toFixed(2);
                totalCost += lineCost;
            });
            
            document.getElementById('total-cost').textContent = '$' + totalCost.toFixed(2);
        }
    });
</script>
{% endblock %}
