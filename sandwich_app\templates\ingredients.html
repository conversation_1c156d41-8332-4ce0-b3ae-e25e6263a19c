{% extends "base.html" %}

{% block title %}Ingredients - Sandwich Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="bi bi-egg text-warning"></i> Ingredient Management
                </h2>
                <a href="{{ url_for('add_ingredient') }}" class="btn btn-warning">
                    <i class="bi bi-plus-circle"></i> Add New Ingredient
                </a>
            </div>

            {% if ingredients %}
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Unit</th>
                                    <th>Supplier</th>
                                    <th>Price per Unit</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ingredient in ingredients %}
                                <tr>
                                    <td>
                                        <strong>{{ ingredient.name }}</strong>
                                    </td>
                                    <td>{{ ingredient.unit }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ ingredient.supplier.name }}</span>
                                    </td>
                                    <td>
                                        <span class="text-success fw-bold">${{ "%.2f"|format(ingredient.price_per_unit) }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('edit_ingredient', ingredient_id=loop.index0) }}" 
                                               class="btn btn-outline-primary">
                                                <i class="bi bi-pencil"></i> Edit
                                            </a>
                                            <button class="btn btn-outline-danger" 
                                                    onclick="confirmDelete('{{ ingredient.name }}', '{{ loop.index0 }}')">
                                                <i class="bi bi-trash"></i> Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="bi bi-egg text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">No Ingredients Found</h4>
                    <p class="text-muted">Get started by adding your first ingredient.</p>
                    <a href="{{ url_for('add_ingredient') }}" class="btn btn-warning">
                        <i class="bi bi-plus-circle"></i> Add First Ingredient
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function confirmDelete(name, id) {
    if (confirm(`Are you sure you want to delete "${name}"?`)) {
        // Add delete functionality here
        window.location.href = `/ingredients/delete/${id}`;
    }
}
</script>
{% endblock %}
