from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from datetime import datetime, date, timedelta
import os
import logging
from .app import <PERSON>wichApp
from .dashboard_simple import Dashboard

class SandwichWebApp:
    def __init__(self):
        self.app = SandwichApp()
        self.dashboard = Dashboard(self.app)
        self.flask_app = Flask(__name__)
        self.flask_app.secret_key = os.environ.get('SECRET_KEY', 'dev-key-for-testing')
        
        # Register routes
        self.setup_routes()
        
        # Add sample data if needed
        if os.environ.get('LOAD_SAMPLE_DATA', 'false').lower() == 'true':
            self.load_sample_data()

    def _parse_date(self, date_str, default_date=None):
        """Helper method to parse date strings with error handling"""
        if not date_str:
            return default_date or date.today()

        try:
            return datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            flash(f'Invalid date format: {date_str}. Using today\'s date.', 'warning')
            return default_date or date.today()

    def _get_shop_by_id(self, shop_id_str, default_to_first=True):
        """Helper method to get shop by ID with validation"""
        if not shop_id_str or not shop_id_str.isdigit():
            if default_to_first and self.app.shops:
                return self.app.shops[0], 0
            return None, None

        shop_id = int(shop_id_str)
        if 0 <= shop_id < len(self.app.shops):
            return self.app.shops[shop_id], shop_id

        flash(f'Invalid shop ID: {shop_id}', 'error')
        if default_to_first and self.app.shops:
            return self.app.shops[0], 0
        return None, None

    def _validate_positive_number(self, value_str, field_name, default=0):
        """Helper method to validate and convert positive numbers"""
        if not value_str:
            return default

        try:
            value = float(value_str)
            if value < 0:
                flash(f'{field_name} cannot be negative', 'error')
                return default
            return value
        except ValueError:
            flash(f'Invalid {field_name}: {value_str}', 'error')
            return default

    def _validate_required_field(self, value, field_name):
        """Helper method to validate required fields"""
        if not value or not value.strip():
            flash(f'{field_name} is required', 'error')
            return False
        return True
    
    def setup_routes(self):
        # Dashboard routes
        @self.flask_app.route('/')
        def home():
            stats = self.dashboard.generate_summary_stats()
            return render_template('dashboard.html', stats=stats)
        
        @self.flask_app.route('/api/charts/sales_trend')
        def api_sales_trend():
            days = request.args.get('days', default=30, type=int)
            svg_content = self.dashboard.plot_daily_sales_trend(days)
            img = self.dashboard.fig_to_base64(svg_content)
            return jsonify({'image': img, 'type': 'svg'})

        @self.flask_app.route('/api/charts/popularity')
        def api_popularity():
            svg_content = self.dashboard.plot_sandwich_popularity()
            img = self.dashboard.fig_to_base64(svg_content)
            return jsonify({'image': img, 'type': 'svg'})

        @self.flask_app.route('/api/charts/shops')
        def api_shops():
            svg_content = self.dashboard.plot_shop_performance()
            img = self.dashboard.fig_to_base64(svg_content)
            return jsonify({'image': img, 'type': 'svg'})
        
        # Sandwich management routes
        @self.flask_app.route('/sandwiches')
        def sandwiches():
            return render_template('sandwiches.html', 
                                  sandwiches=self.app.sandwich_types,
                                  ingredients=self.app.ingredients)
        
        @self.flask_app.route('/sandwiches/add', methods=['GET', 'POST'])
        def add_sandwich():
            if request.method == 'POST':
                name = request.form.get('name', '').strip()
                description = request.form.get('description', '').strip()
                price_str = request.form.get('price', '0')

                # Validate required fields
                if not self._validate_required_field(name, 'Sandwich name'):
                    return render_template('add_sandwich.html', ingredients=self.app.ingredients)

                # Validate price
                price = self._validate_positive_number(price_str, 'Price')
                if price == 0:
                    flash('Price must be greater than 0', 'error')
                    return render_template('add_sandwich.html', ingredients=self.app.ingredients)

                try:
                    sandwich = self.app.add_sandwich_type(name, description)
                    sandwich.set_price(price)

                    # Add ingredients if specified
                    ingredient_ids = request.form.getlist('ingredient_id')
                    quantities = request.form.getlist('quantity')

                    for i in range(len(ingredient_ids)):
                        if i < len(quantities) and quantities[i]:
                            try:
                                ingredient_id = int(ingredient_ids[i])
                                quantity = self._validate_positive_number(quantities[i], 'Ingredient quantity')

                                if 0 <= ingredient_id < len(self.app.ingredients) and quantity > 0:
                                    ingredient = self.app.ingredients[ingredient_id]
                                    sandwich.add_ingredient(ingredient, quantity)
                            except (ValueError, IndexError):
                                flash(f'Invalid ingredient data at position {i+1}', 'warning')
                                continue

                    flash(f'Sandwich "{name}" added successfully!', 'success')
                    return redirect(url_for('sandwiches'))

                except Exception as e:
                    flash(f'Error adding sandwich: {str(e)}', 'error')
                    return render_template('add_sandwich.html', ingredients=self.app.ingredients)

            return render_template('add_sandwich.html', ingredients=self.app.ingredients)
        
        # Supplier and ingredient routes
        @self.flask_app.route('/suppliers')
        def suppliers():
            return render_template('suppliers.html', suppliers=self.app.suppliers)
        
        @self.flask_app.route('/suppliers/add', methods=['GET', 'POST'])
        def add_supplier():
            if request.method == 'POST':
                name = request.form.get('name')
                contact = request.form.get('contact')
                
                self.app.add_supplier(name, contact)
                flash(f'Supplier {name} added successfully!', 'success')
                return redirect(url_for('suppliers'))
                
            return render_template('add_supplier.html')
        
        @self.flask_app.route('/ingredients')
        def ingredients():
            return render_template('ingredients.html', 
                                  ingredients=self.app.ingredients,
                                  suppliers=self.app.suppliers)
        
        @self.flask_app.route('/ingredients/add', methods=['GET', 'POST'])
        def add_ingredient():
            if request.method == 'POST':
                name = request.form.get('name')
                unit = request.form.get('unit')
                price = float(request.form.get('price', 0))
                supplier_id = int(request.form.get('supplier_id', 0))
                
                if supplier_id < len(self.app.suppliers):
                    supplier = self.app.suppliers[supplier_id]
                    self.app.add_ingredient(name, unit, supplier, price)
                    flash(f'Ingredient {name} added successfully!', 'success')
                else:
                    flash('Invalid supplier selected', 'error')
                    
                return redirect(url_for('ingredients'))
                
            return render_template('add_ingredient.html', suppliers=self.app.suppliers)
        
        # Shop management routes
        @self.flask_app.route('/shops')
        def shops():
            return render_template('shops.html', shops=self.app.shops)
        
        @self.flask_app.route('/shops/add', methods=['GET', 'POST'])
        def add_shop():
            if request.method == 'POST':
                name = request.form.get('name')
                location = request.form.get('location')
                
                self.app.add_shop(name, location)
                flash(f'Shop {name} added successfully!', 'success')
                return redirect(url_for('shops'))
                
            return render_template('add_shop.html')
        
        # Daily planning routes
        @self.flask_app.route('/daily-plan', methods=['GET', 'POST'])
        def daily_plan():
            plan_date = self._parse_date(request.args.get('date'))

            plan = self.app.get_daily_plan(plan_date)
            if not plan:
                plan = self.app.create_daily_plan(plan_date)

            if request.method == 'POST':
                try:
                    # Update planned quantities using sandwich index instead of id()
                    for i, sandwich in enumerate(self.app.sandwich_types):
                        quantity_key = f'planned_{i}'
                        if quantity_key in request.form:
                            quantity = self._validate_positive_number(
                                request.form.get(quantity_key, '0'),
                                f'Planned quantity for {sandwich.name}'
                            )
                            plan.plan_quantity(sandwich, int(quantity))

                    flash('Daily plan updated successfully!', 'success')
                except Exception as e:
                    flash(f'Error updating daily plan: {str(e)}', 'error')

                return redirect(url_for('daily_plan', date=plan_date.strftime('%Y-%m-%d')))

            return render_template('daily_plan.html',
                                  plan=plan,
                                  plan_date=plan_date,
                                  sandwiches=self.app.sandwich_types)
        
        @self.flask_app.route('/production', methods=['GET', 'POST'])
        def production():
            selected_date = request.args.get('date')
            if selected_date:
                plan_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            else:
                plan_date = date.today()
                
            plan = self.app.get_daily_plan(plan_date)
            if not plan:
                plan = self.app.create_daily_plan(plan_date)
                
            if request.method == 'POST':
                # Update produced quantities
                for sandwich in self.app.sandwich_types:
                    quantity_key = f'produced_{id(sandwich)}'
                    if quantity_key in request.form:
                        quantity = int(request.form.get(quantity_key, 0))
                        plan.record_production(sandwich, quantity)
                
                flash('Production records updated successfully!', 'success')
                return redirect(url_for('production', date=plan_date.strftime('%Y-%m-%d')))
                
            return render_template('production.html', 
                                  plan=plan,
                                  plan_date=plan_date,
                                  sandwiches=self.app.sandwich_types)
        
        @self.flask_app.route('/deliveries', methods=['GET', 'POST'])
        def deliveries():
            plan_date = self._parse_date(request.args.get('date'))
            selected_shop, shop_id = self._get_shop_by_id(request.args.get('shop_id'))

            plan = self.app.get_daily_plan(plan_date)
            if not plan:
                plan = self.app.create_daily_plan(plan_date)

            if request.method == 'POST' and selected_shop:
                try:
                    # Update deliveries using sandwich index instead of id()
                    for i, sandwich in enumerate(self.app.sandwich_types):
                        delivery_key = f'delivery_{i}'
                        if delivery_key in request.form:
                            quantity = self._validate_positive_number(
                                request.form.get(delivery_key, '0'),
                                f'Delivery quantity for {sandwich.name}'
                            )
                            plan.record_delivery(selected_shop, sandwich, int(quantity))

                    flash(f'Deliveries to {selected_shop.name} updated successfully!', 'success')
                except Exception as e:
                    flash(f'Error updating deliveries: {str(e)}', 'error')

                return redirect(url_for('deliveries',
                                       date=plan_date.strftime('%Y-%m-%d'),
                                       shop_id=shop_id))

            return render_template('deliveries.html',
                                  plan=plan,
                                  plan_date=plan_date,
                                  shops=self.app.shops,
                                  selected_shop=selected_shop,
                                  sandwiches=self.app.sandwich_types)
        
        @self.flask_app.route('/returns', methods=['GET', 'POST'])
        def returns():
            selected_date = request.args.get('date')
            if selected_date:
                plan_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            else:
                plan_date = date.today()
                
            selected_shop_id = request.args.get('shop_id')
            if selected_shop_id and selected_shop_id.isdigit():
                shop_id = int(selected_shop_id)
                if 0 <= shop_id < len(self.app.shops):
                    selected_shop = self.app.shops[shop_id]
                else:
                    selected_shop = None
            else:
                selected_shop = self.app.shops[0] if self.app.shops else None
                
            plan = self.app.get_daily_plan(plan_date)
            if not plan:
                plan = self.app.create_daily_plan(plan_date)
                
            if request.method == 'POST' and selected_shop:
                # Update returns
                for sandwich in self.app.sandwich_types:
                    return_key = f'return_{id(sandwich)}'
                    if return_key in request.form:
                        quantity = int(request.form.get(return_key, 0))
                        plan.record_returns(selected_shop, sandwich, quantity)
                
                flash(f'Returns from {selected_shop.name} updated successfully!', 'success')
                return redirect(url_for('returns', 
                                       date=plan_date.strftime('%Y-%m-%d'),
                                       shop_id=shop_id))
                
            return render_template('returns.html', 
                                  plan=plan,
                                  plan_date=plan_date,
                                  shops=self.app.shops,
                                  selected_shop=selected_shop,
                                  sandwiches=self.app.sandwich_types)
        
        # Reports
        @self.flask_app.route('/reports')
        def reports():
            return render_template('reports.html')
        
        @self.flask_app.route('/reports/daily-summary')
        def daily_summary_report():
            selected_date = request.args.get('date')
            if selected_date:
                report_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            else:
                report_date = date.today()
                
            summary = self.app.get_daily_summary(report_date)
            
            return render_template('daily_summary.html', 
                                  summary=summary,
                                  report_date=report_date)
        
        @self.flask_app.route('/reports/shop-invoice')
        def shop_invoice_report():
            shop_id = request.args.get('shop_id')
            start_date = request.args.get('start_date')
            end_date = request.args.get('end_date')
            
            if not shop_id or not shop_id.isdigit():
                shop = self.app.shops[0] if self.app.shops else None
            else:
                shop_id = int(shop_id)
                shop = self.app.shops[shop_id] if 0 <= shop_id < len(self.app.shops) else None
            
            if not start_date:
                start_date = (date.today() - timedelta(days=7)).strftime('%Y-%m-%d')
            
            if not end_date:
                end_date = date.today().strftime('%Y-%m-%d')
                
            start = datetime.strptime(start_date, '%Y-%m-%d').date()
            end = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            invoice = self.app.generate_shop_invoice(shop, start, end)

            return render_template('shop_invoice.html',
                                  invoice=invoice,
                                  shop=shop,
                                  shops=self.app.shops,
                                  start_date=start_date,
                                  end_date=end_date)

    def load_sample_data(self):
        """Load sample data for testing purposes"""
        # Add sample suppliers
        supplier1 = self.app.add_supplier("Fresh Foods Co", "<EMAIL>")
        supplier2 = self.app.add_supplier("Bakery Supply Inc", "<EMAIL>")

        # Add sample ingredients
        bread = self.app.add_ingredient("Bread", "loaf", supplier2, 2.50)
        ham = self.app.add_ingredient("Ham", "kg", supplier1, 12.00)
        cheese = self.app.add_ingredient("Cheese", "kg", supplier1, 8.50)
        lettuce = self.app.add_ingredient("Lettuce", "head", supplier1, 1.20)
        tomato = self.app.add_ingredient("Tomato", "kg", supplier1, 3.00)
        mayo = self.app.add_ingredient("Mayonnaise", "jar", supplier1, 4.50)

        # Add sample sandwich types
        ham_cheese = self.app.add_sandwich_type("Ham & Cheese", "Classic ham and cheese sandwich")
        ham_cheese.add_ingredient(bread, 0.2)  # 1/5 of a loaf
        ham_cheese.add_ingredient(ham, 0.1)    # 100g
        ham_cheese.add_ingredient(cheese, 0.05) # 50g
        ham_cheese.set_price(6.50)

        club = self.app.add_sandwich_type("Club Sandwich", "Triple-decker with ham, cheese, lettuce, tomato")
        club.add_ingredient(bread, 0.3)
        club.add_ingredient(ham, 0.15)
        club.add_ingredient(cheese, 0.08)
        club.add_ingredient(lettuce, 0.1)
        club.add_ingredient(tomato, 0.1)
        club.add_ingredient(mayo, 0.02)
        club.set_price(8.50)

        veggie = self.app.add_sandwich_type("Veggie Delight", "Fresh vegetables with cheese")
        veggie.add_ingredient(bread, 0.2)
        veggie.add_ingredient(cheese, 0.06)
        veggie.add_ingredient(lettuce, 0.15)
        veggie.add_ingredient(tomato, 0.15)
        veggie.add_ingredient(mayo, 0.02)
        veggie.set_price(5.50)

        # Add sample shops
        shop1 = self.app.add_shop("Downtown Deli", "123 Main St, Downtown")
        shop2 = self.app.add_shop("Campus Corner", "456 University Ave, Campus")
        shop3 = self.app.add_shop("Mall Food Court", "789 Shopping Center, Mall")

        # Create sample daily plans with data
        from datetime import date, timedelta

        for i in range(7):  # Last 7 days
            plan_date = date.today() - timedelta(days=i)
            plan = self.app.create_daily_plan(plan_date)

            # Plan quantities
            plan.plan_quantity(ham_cheese, 50)
            plan.plan_quantity(club, 30)
            plan.plan_quantity(veggie, 25)

            # Record production
            plan.record_production(ham_cheese, 48)
            plan.record_production(club, 28)
            plan.record_production(veggie, 24)

            # Record deliveries and returns
            plan.record_delivery(shop1, ham_cheese, 20)
            plan.record_delivery(shop1, club, 12)
            plan.record_delivery(shop1, veggie, 10)

            plan.record_delivery(shop2, ham_cheese, 15)
            plan.record_delivery(shop2, club, 10)
            plan.record_delivery(shop2, veggie, 8)

            plan.record_delivery(shop3, ham_cheese, 13)
            plan.record_delivery(shop3, club, 6)
            plan.record_delivery(shop3, veggie, 6)

            # Some returns (unsold items)
            plan.record_returns(shop1, ham_cheese, 2)
            plan.record_returns(shop2, club, 1)
            plan.record_returns(shop3, veggie, 1)

    def run(self, debug=True, port=5000, host='127.0.0.1'):
        """Run the Flask application"""
        self.flask_app.run(debug=debug, port=port, host=host)