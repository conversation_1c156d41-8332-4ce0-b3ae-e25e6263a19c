{% extends "base.html" %}

{% block title %}Shop Invoice - Sandwich Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-receipt"></i> Shop Invoice</h1>
    <button onclick="window.print()" class="btn btn-outline-secondary">
        <i class="bi bi-printer"></i> Print Invoice
    </button>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <!-- Invoice Header -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h3>Sandwich Management System</h3>
                        <p class="mb-0">
                            123 Business Street<br>
                            City, State 12345<br>
                            Phone: (*************
                        </p>
                    </div>
                    <div class="col-md-6 text-end">
                        <h4>INVOICE</h4>
                        <p class="mb-0">
                            <strong>Invoice #:</strong> INV-{{ shop.name[:3].upper() }}-{{ start_date.strftime('%Y%m%d') }}<br>
                            <strong>Date:</strong> {{ start_date.strftime('%B %d, %Y') }}<br>
                            <strong>Period:</strong> {{ start_date.strftime('%m/%d/%Y') }} - {{ end_date.strftime('%m/%d/%Y') }}
                        </p>
                    </div>
                </div>
                
                <!-- Bill To -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>Bill To:</h5>
                        <p class="mb-0">
                            <strong>{{ shop.name }}</strong><br>
                            {% if shop.location %}
                                {{ shop.location }}
                            {% else %}
                                Address not specified
                            {% endif %}
                        </p>
                    </div>
                </div>
                
                <!-- Invoice Details -->
                {% if invoice and invoice.line_items %}
                    <div class="table-responsive mb-4">
                        <table class="table table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>Date</th>
                                    <th>Item</th>
                                    <th>Delivered</th>
                                    <th>Returned</th>
                                    <th>Net Sold</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in invoice.line_items %}
                                    <tr>
                                        <td>{{ item.date.strftime('%m/%d/%Y') }}</td>
                                        <td>{{ item.sandwich_name }}</td>
                                        <td>{{ item.delivered }}</td>
                                        <td>{{ item.returned }}</td>
                                        <td>{{ item.net_sold }}</td>
                                        <td>${{ "%.2f"|format(item.unit_price) }}</td>
                                        <td>${{ "%.2f"|format(item.line_total) }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th colspan="4">Totals:</th>
                                    <th>{{ invoice.total_delivered }}</th>
                                    <th>{{ invoice.total_returned }}</th>
                                    <th>{{ invoice.total_net_sold }}</th>
                                    <th></th>
                                    <th>${{ "%.2f"|format(invoice.total_amount) }}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    
                    <!-- Summary -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>Summary</h6>
                                    <p class="mb-1"><strong>Total Items Delivered:</strong> {{ invoice.total_delivered }}</p>
                                    <p class="mb-1"><strong>Total Items Returned:</strong> {{ invoice.total_returned }}</p>
                                    <p class="mb-1"><strong>Net Items Sold:</strong> {{ invoice.total_net_sold }}</p>
                                    <p class="mb-0">
                                        <strong>Return Rate:</strong> 
                                        {% if invoice.total_delivered > 0 %}
                                            {{ "%.1f"|format((invoice.total_returned / invoice.total_delivered) * 100) }}%
                                        {% else %}
                                            0%
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-end">
                                    <h6>Amount Due</h6>
                                    <h3>${{ "%.2f"|format(invoice.total_amount) }}</h3>
                                    <p class="mb-0">Payment due within 30 days</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Payment Terms -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <h6>Payment Terms & Notes</h6>
                                    <p class="mb-1"><strong>Payment Terms:</strong> Net 30 days</p>
                                    <p class="mb-1"><strong>Late Fee:</strong> 1.5% per month on overdue amounts</p>
                                    <p class="mb-0"><strong>Payment Methods:</strong> Check, Bank Transfer, or Cash</p>
                                </div>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-inbox display-4 text-muted"></i>
                        <h4 class="text-muted">No transactions found</h4>
                        <p class="text-muted">No deliveries or sales recorded for {{ shop.name }} during the selected period.</p>
                    </div>
                {% endif %}
                
                <!-- Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex gap-2 justify-content-center">
                            <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Back to Reports
                            </a>
                            <a href="{{ url_for('shop_invoice_report') }}" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-clockwise"></i> Generate New Invoice
                            </a>
                            {% if invoice and invoice.line_items %}
                                <button onclick="window.print()" class="btn btn-primary">
                                    <i class="bi bi-printer"></i> Print Invoice
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    @media print {
        .btn, .navbar, .card-header, .alert {
            display: none !important;
        }
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        .card-body {
            padding: 0 !important;
        }
        body {
            font-size: 12px;
        }
        h1, h2, h3, h4, h5, h6 {
            color: black !important;
        }
        .table {
            font-size: 11px;
        }
    }
</style>
{% endblock %}
