{% extends "base.html" %}

{% block title %}Sandwiches - Sandwich Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-list-ul"></i> Sandwiches</h1>
    <a href="{{ url_for('add_sandwich') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> Add New Sandwich
    </a>
</div>

{% if sandwiches %}
    <div class="row">
        {% for sandwich in sandwiches %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">{{ sandwich.name }}</h5>
                        <span class="badge bg-success">${{ "%.2f"|format(sandwich.selling_price) }}</span>
                    </div>
                    <div class="card-body">
                        {% if sandwich.description %}
                            <p class="card-text">{{ sandwich.description }}</p>
                        {% endif %}
                        
                        <h6>Ingredients:</h6>
                        {% if sandwich.recipe %}
                            <ul class="list-unstyled">
                                {% for ingredient, quantity in sandwich.recipe.items() %}
                                    <li>
                                        <small class="text-muted">
                                            {{ ingredient.name }}: {{ quantity }} {{ ingredient.unit }}
                                        </small>
                                    </li>
                                {% endfor %}
                            </ul>
                        {% else %}
                            <p class="text-muted"><small>No ingredients added yet</small></p>
                        {% endif %}
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                Cost: ${{ "%.2f"|format(sandwich.calculate_cost()) }} | 
                                Margin: {{ "%.1f"|format(sandwich.calculate_profit_margin()) }}%
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="bi bi-inbox display-1 text-muted"></i>
        <h3 class="text-muted">No sandwiches yet</h3>
        <p class="text-muted">Start by adding your first sandwich type.</p>
        <a href="{{ url_for('add_sandwich') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Add First Sandwich
        </a>
    </div>
{% endif %}
{% endblock %}
