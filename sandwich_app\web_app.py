from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from datetime import datetime, date, timedelta
import os
from .app import SandwichApp
from .dashboard import Dashboard

class SandwichWebApp:
    def __init__(self):
        self.app = SandwichApp()
        self.dashboard = Dashboard(self.app)
        self.flask_app = Flask(__name__)
        self.flask_app.secret_key = os.environ.get('SECRET_KEY', 'dev-key-for-testing')
        
        # Register routes
        self.setup_routes()
        
        # Add sample data if needed
        if os.environ.get('LOAD_SAMPLE_DATA', 'false').lower() == 'true':
            self.load_sample_data()
    
    def setup_routes(self):
        # Dashboard routes
        @self.flask_app.route('/')
        def home():
            stats = self.dashboard.generate_summary_stats()
            return render_template('dashboard.html', stats=stats)
        
        @self.flask_app.route('/api/charts/sales_trend')
        def api_sales_trend():
            days = request.args.get('days', default=30, type=int)
            fig = self.dashboard.plot_daily_sales_trend(days)
            img = self.dashboard.fig_to_base64(fig)
            return jsonify({'image': img})
        
        @self.flask_app.route('/api/charts/popularity')
        def api_popularity():
            fig = self.dashboard.plot_sandwich_popularity()
            img = self.dashboard.fig_to_base64(fig)
            return jsonify({'image': img})
        
        @self.flask_app.route('/api/charts/shops')
        def api_shops():
            fig = self.dashboard.plot_shop_performance()
            img = self.dashboard.fig_to_base64(fig)
            return jsonify({'image': img})
        
        # Sandwich management routes
        @self.flask_app.route('/sandwiches')
        def sandwiches():
            return render_template('sandwiches.html', 
                                  sandwiches=self.app.sandwich_types,
                                  ingredients=self.app.ingredients)
        
        @self.flask_app.route('/sandwiches/add', methods=['GET', 'POST'])
        def add_sandwich():
            if request.method == 'POST':
                name = request.form.get('name')
                description = request.form.get('description')
                price = float(request.form.get('price', 0))
                
                sandwich = self.app.add_sandwich_type(name, description)
                sandwich.set_price(price)
                
                # Add ingredients if specified
                ingredient_ids = request.form.getlist('ingredient_id')
                quantities = request.form.getlist('quantity')
                
                for i in range(len(ingredient_ids)):
                    if i < len(quantities) and quantities[i]:
                        ingredient_id = int(ingredient_ids[i])
                        quantity = float(quantities[i])
                        if ingredient_id < len(self.app.ingredients):
                            ingredient = self.app.ingredients[ingredient_id]
                            sandwich.add_ingredient(ingredient, quantity)
                
                flash(f'Sandwich {name} added successfully!', 'success')
                return redirect(url_for('sandwiches'))
                
            return render_template('add_sandwich.html', ingredients=self.app.ingredients)
        
        # Supplier and ingredient routes
        @self.flask_app.route('/suppliers')
        def suppliers():
            return render_template('suppliers.html', suppliers=self.app.suppliers)
        
        @self.flask_app.route('/suppliers/add', methods=['GET', 'POST'])
        def add_supplier():
            if request.method == 'POST':
                name = request.form.get('name')
                contact = request.form.get('contact')
                
                self.app.add_supplier(name, contact)
                flash(f'Supplier {name} added successfully!', 'success')
                return redirect(url_for('suppliers'))
                
            return render_template('add_supplier.html')
        
        @self.flask_app.route('/ingredients')
        def ingredients():
            return render_template('ingredients.html', 
                                  ingredients=self.app.ingredients,
                                  suppliers=self.app.suppliers)
        
        @self.flask_app.route('/ingredients/add', methods=['GET', 'POST'])
        def add_ingredient():
            if request.method == 'POST':
                name = request.form.get('name')
                unit = request.form.get('unit')
                price = float(request.form.get('price', 0))
                supplier_id = int(request.form.get('supplier_id', 0))
                
                if supplier_id < len(self.app.suppliers):
                    supplier = self.app.suppliers[supplier_id]
                    self.app.add_ingredient(name, unit, supplier, price)
                    flash(f'Ingredient {name} added successfully!', 'success')
                else:
                    flash('Invalid supplier selected', 'error')
                    
                return redirect(url_for('ingredients'))
                
            return render_template('add_ingredient.html', suppliers=self.app.suppliers)
        
        # Shop management routes
        @self.flask_app.route('/shops')
        def shops():
            return render_template('shops.html', shops=self.app.shops)
        
        @self.flask_app.route('/shops/add', methods=['GET', 'POST'])
        def add_shop():
            if request.method == 'POST':
                name = request.form.get('name')
                location = request.form.get('location')
                
                self.app.add_shop(name, location)
                flash(f'Shop {name} added successfully!', 'success')
                return redirect(url_for('shops'))
                
            return render_template('add_shop.html')
        
        # Daily planning routes
        @self.flask_app.route('/daily-plan', methods=['GET', 'POST'])
        def daily_plan():
            selected_date = request.args.get('date')
            if selected_date:
                plan_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            else:
                plan_date = date.today()
                
            plan = self.app.get_daily_plan(plan_date)
            if not plan:
                plan = self.app.create_daily_plan(plan_date)
                
            if request.method == 'POST':
                # Update planned quantities
                for sandwich in self.app.sandwich_types:
                    quantity_key = f'planned_{id(sandwich)}'
                    if quantity_key in request.form:
                        quantity = int(request.form.get(quantity_key, 0))
                        plan.plan_quantity(sandwich, quantity)
                
                flash('Daily plan updated successfully!', 'success')
                return redirect(url_for('daily_plan', date=plan_date.strftime('%Y-%m-%d')))
                
            return render_template('daily_plan.html', 
                                  plan=plan,
                                  plan_date=plan_date,
                                  sandwiches=self.app.sandwich_types)
        
        @self.flask_app.route('/production', methods=['GET', 'POST'])
        def production():
            selected_date = request.args.get('date')
            if selected_date:
                plan_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            else:
                plan_date = date.today()
                
            plan = self.app.get_daily_plan(plan_date)
            if not plan:
                plan = self.app.create_daily_plan(plan_date)
                
            if request.method == 'POST':
                # Update produced quantities
                for sandwich in self.app.sandwich_types:
                    quantity_key = f'produced_{id(sandwich)}'
                    if quantity_key in request.form:
                        quantity = int(request.form.get(quantity_key, 0))
                        plan.record_production(sandwich, quantity)
                
                flash('Production records updated successfully!', 'success')
                return redirect(url_for('production', date=plan_date.strftime('%Y-%m-%d')))
                
            return render_template('production.html', 
                                  plan=plan,
                                  plan_date=plan_date,
                                  sandwiches=self.app.sandwich_types)
        
        @self.flask_app.route('/deliveries', methods=['GET', 'POST'])
        def deliveries():
            selected_date = request.args.get('date')
            if selected_date:
                plan_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            else:
                plan_date = date.today()
                
            selected_shop_id = request.args.get('shop_id')
            if selected_shop_id and selected_shop_id.isdigit():
                shop_id = int(selected_shop_id)
                if 0 <= shop_id < len(self.app.shops):
                    selected_shop = self.app.shops[shop_id]
                else:
                    selected_shop = None
            else:
                selected_shop = self.app.shops[0] if self.app.shops else None
                
            plan = self.app.get_daily_plan(plan_date)
            if not plan:
                plan = self.app.create_daily_plan(plan_date)
                
            if request.method == 'POST' and selected_shop:
                # Update deliveries
                for sandwich in self.app.sandwich_types:
                    delivery_key = f'delivery_{id(sandwich)}'
                    if delivery_key in request.form:
                        quantity = int(request.form.get(delivery_key, 0))
                        plan.record_delivery(selected_shop, sandwich, quantity)
                
                flash(f'Deliveries to {selected_shop.name} updated successfully!', 'success')
                return redirect(url_for('deliveries', 
                                       date=plan_date.strftime('%Y-%m-%d'),
                                       shop_id=shop_id))
                
            return render_template('deliveries.html', 
                                  plan=plan,
                                  plan_date=plan_date,
                                  shops=self.app.shops,
                                  selected_shop=selected_shop,
                                  sandwiches=self.app.sandwich_types)
        
        @self.flask_app.route('/returns', methods=['GET', 'POST'])
        def returns():
            selected_date = request.args.get('date')
            if selected_date:
                plan_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            else:
                plan_date = date.today()
                
            selected_shop_id = request.args.get('shop_id')
            if selected_shop_id and selected_shop_id.isdigit():
                shop_id = int(selected_shop_id)
                if 0 <= shop_id < len(self.app.shops):
                    selected_shop = self.app.shops[shop_id]
                else:
                    selected_shop = None
            else:
                selected_shop = self.app.shops[0] if self.app.shops else None
                
            plan = self.app.get_daily_plan(plan_date)
            if not plan:
                plan = self.app.create_daily_plan(plan_date)
                
            if request.method == 'POST' and selected_shop:
                # Update returns
                for sandwich in self.app.sandwich_types:
                    return_key = f'return_{id(sandwich)}'
                    if return_key in request.form:
                        quantity = int(request.form.get(return_key, 0))
                        plan.record_returns(selected_shop, sandwich, quantity)
                
                flash(f'Returns from {selected_shop.name} updated successfully!', 'success')
                return redirect(url_for('returns', 
                                       date=plan_date.strftime('%Y-%m-%d'),
                                       shop_id=shop_id))
                
            return render_template('returns.html', 
                                  plan=plan,
                                  plan_date=plan_date,
                                  shops=self.app.shops,
                                  selected_shop=selected_shop,
                                  sandwiches=self.app.sandwich_types)
        
        # Reports
        @self.flask_app.route('/reports')
        def reports():
            return render_template('reports.html')
        
        @self.flask_app.route('/reports/daily-summary')
        def daily_summary_report():
            selected_date = request.args.get('date')
            if selected_date:
                report_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
            else:
                report_date = date.today()
                
            summary = self.app.get_daily_summary(report_date)
            
            return render_template('daily_summary.html', 
                                  summary=summary,
                                  report_date=report_date)
        
        @self.flask_app.route('/reports/shop-invoice')
        def shop_invoice_report():
            shop_id = request.args.get('shop_id')
            start_date = request.args.get('start_date')
            end_date = request.args.get('end_date')
            
            if not shop_id or not shop_id.isdigit():
                shop = self.app.shops[0] if self.app.shops else None
            else:
                shop_id = int(shop_id)
                shop = self.app.shops[shop_id] if 0 <= shop_id < len(self.app.shops) else None
            
            if not start_date:
                start_date = (date.today() - timedelta(days=7)).strftime('%Y-%m-%d')
            
            if not end_date:
                end_date = date.today().strftime('%Y-%m-%d')
                
            start = datetime.strptime(start_date, '%Y-%m-%d').date()
            end = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            invoice =