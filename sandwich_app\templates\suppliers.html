{% extends "base.html" %}

{% block title %}Suppliers - Sandwich Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="bi bi-truck text-info"></i> Supplier Management
                </h2>
                <a href="{{ url_for('add_supplier') }}" class="btn btn-info">
                    <i class="bi bi-plus-circle"></i> Add New Supplier
                </a>
            </div>

            {% if suppliers %}
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Contact Info</th>
                                    <th>Ingredients Supplied</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for supplier in suppliers %}
                                <tr>
                                    <td>
                                        <strong>{{ supplier.name }}</strong>
                                    </td>
                                    <td>{{ supplier.contact_info or '-' }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ supplier.get_ingredients_count() }} ingredients</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('edit_supplier', supplier_id=loop.index0) }}" 
                                               class="btn btn-outline-primary">
                                                <i class="bi bi-pencil"></i> Edit
                                            </a>
                                            <button class="btn btn-outline-danger" 
                                                    onclick="confirmDelete('{{ supplier.name }}', '{{ loop.index0 }}')">
                                                <i class="bi bi-trash"></i> Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="bi bi-truck text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">No Suppliers Found</h4>
                    <p class="text-muted">Get started by adding your first supplier.</p>
                    <a href="{{ url_for('add_supplier') }}" class="btn btn-info">
                        <i class="bi bi-plus-circle"></i> Add First Supplier
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function confirmDelete(name, id) {
    if (confirm(`Are you sure you want to delete "${name}"?`)) {
        // Add delete functionality here
        window.location.href = `/suppliers/delete/${id}`;
    }
}
</script>
{% endblock %}
