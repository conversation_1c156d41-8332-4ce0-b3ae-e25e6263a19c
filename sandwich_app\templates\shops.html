{% extends "base.html" %}

{% block title %}Shops - Sandwich Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-shop"></i> Shops</h1>
    <a href="{{ url_for('add_shop') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> Add New Shop
    </a>
</div>

{% if shops %}
    <div class="row">
        {% for shop in shops %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">{{ shop.name }}</h5>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('edit_shop', shop_id=loop.index0) }}">
                                    <i class="bi bi-pencil"></i> Edit
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('deliveries', shop_id=loop.index0) }}">
                                    <i class="bi bi-truck"></i> Manage Deliveries
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('returns', shop_id=loop.index0) }}">
                                    <i class="bi bi-arrow-return-left"></i> Manage Returns
                                </a></li>
                                <li><a class="dropdown-item" href="{{ url_for('shop_invoice_report', shop_id=loop.index0) }}">
                                    <i class="bi bi-file-text"></i> View Invoice
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="confirmDelete('{{ shop.name }}', '{{ url_for('delete_shop', shop_id=loop.index0) }}')">
                                    <i class="bi bi-trash"></i> Delete
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if shop.location %}
                            <p class="card-text">
                                <i class="bi bi-geo-alt"></i> {{ shop.location }}
                            </p>
                        {% else %}
                            <p class="text-muted"><small>No location specified</small></p>
                        {% endif %}
                        
                        <!-- Show recent performance stats -->
                        {% set shop_stats = {"deliveries": 0, "returns": 0, "sales": 0} %}
                        {% for plan_date, plan in daily_plans.items() if plan_date >= (today - timedelta(days=7)) %}
                            {% for sandwich in sandwiches %}
                                {% set delivered = plan.shop_deliveries.get(shop, {}).get(sandwich, 0) %}
                                {% set returned = plan.shop_returns.get(shop, {}).get(sandwich, 0) %}
                                {% set _ = shop_stats.update({"deliveries": shop_stats.deliveries + delivered}) %}
                                {% set _ = shop_stats.update({"returns": shop_stats.returns + returned}) %}
                                {% set _ = shop_stats.update({"sales": shop_stats.sales + (delivered - returned)}) %}
                            {% endfor %}
                        {% endfor %}
                        
                        <div class="row text-center mt-3">
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="text-primary">{{ shop_stats.sales }}</h6>
                                    <small class="text-muted">Sold (7d)</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="text-success">{{ shop_stats.deliveries }}</h6>
                                    <small class="text-muted">Delivered (7d)</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <h6 class="text-warning">{{ shop_stats.returns }}</h6>
                                <small class="text-muted">Returned (7d)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="bi bi-shop display-1 text-muted"></i>
        <h3 class="text-muted">No shops yet</h3>
        <p class="text-muted">Start by adding your first shop location.</p>
        <a href="{{ url_for('add_shop') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Add First Shop
        </a>
    </div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete shop <strong id="deleteShopName"></strong>?</p>
                <p class="text-warning"><small>This will also remove all delivery and return records for this shop.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">Delete</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function confirmDelete(shopName, deleteUrl) {
        document.getElementById('deleteShopName').textContent = shopName;
        document.getElementById('confirmDeleteBtn').href = deleteUrl;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }
</script>
{% endblock %}
