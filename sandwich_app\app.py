from datetime import date, timedelta
from .models import Supplier, Ingredient, SandwichType, Shop, DailyPlan

class SandwichApp:
    def __init__(self):
        self.sandwich_types = []
        self.suppliers = []
        self.ingredients = []
        self.shops = []
        self.daily_plans = {}  # date: DailyPlan
        
    def add_supplier(self, name, contact_info=""):
        supplier = Supplier(name, contact_info)
        self.suppliers.append(supplier)
        return supplier
        
    def add_ingredient(self, name, unit, supplier, price_per_unit):
        ingredient = Ingredient(name, unit, supplier, price_per_unit)
        self.ingredients.append(ingredient)
        return ingredient
        
    def add_sandwich_type(self, name, description=""):
        sandwich = SandwichType(name, description)
        self.sandwich_types.append(sandwich)
        return sandwich
        
    def add_shop(self, name, location=""):
        shop = Shop(name, location)
        self.shops.append(shop)
        return shop
        
    def create_daily_plan(self, plan_date=None):
        if plan_date is None:
            plan_date = date.today()
        plan = DailyPlan(plan_date)
        self.daily_plans[plan_date] = plan
        return plan
    
    def get_daily_plan(self, plan_date=None):
        if plan_date is None:
            plan_date = date.today()
        return self.daily_plans.get(plan_date)
        
    def get_daily_summary(self, plan_date=None):
        if plan_date is None:
            plan_date = date.today()
        plan = self.daily_plans.get(plan_date)
        if not plan:
            return None
            
        summary = {
            "date": plan_date,
            "sandwiches": {},
            "total_planned": 0,
            "total_produced": 0,
            "total_sold": 0,
            "total_returned": 0,
            "revenue": plan.calculate_daily_revenue(),
            "costs": plan.calculate_daily_costs(),
            "profit": plan.calculate_daily_profit()
        }
            
        for sandwich in self.sandwich_types:
            planned = plan.planned_quantities.get(sandwich, 0)
            produced = plan.produced_quantities.get(sandwich, 0)
            sold = plan.get_total_sold(sandwich)
            returned = sum(plan.shop_returns.get(shop, {}).get(sandwich, 0) for shop in self.shops)
            
            summary["sandwiches"][sandwich.name] = {
                "planned": planned,
                "produced": produced,
                "sold": sold,
                "returned": returned,
                "revenue": sold * sandwich.selling_price,
                "cost_per_unit": sandwich.calculate_cost(),
                "total_cost": produced * sandwich.calculate_cost(),
                "profit": sold * sandwich.selling_price - produced * sandwich.calculate_cost()
            }
            
            summary["total_planned"] += planned
            summary["total_produced"] += produced
            summary["total_sold"] += sold
            summary["total_returned"] += returned
            
        return summary
        
    def generate_shop_invoice(self, shop, start_date, end_date):
        invoice = {"shop": shop.name, "items": [], "total": 0}
        
        current_date = start_date
        while current_date <= end_date:
            plan = self.daily_plans.get(current_date)
            if plan:
                for sandwich_type in self.sandwich_types:
                    sold = plan.get_sold_by_shop(shop, sandwich_type)
                    if sold > 0:
                        amount = sold * sandwich_type.selling_price
                        invoice["items"].append({
                            "date": current_date,
                            "sandwich": sandwich_type.name,
                            "quantity": sold,
                            "price": sandwich_type.selling_price,
                            "amount": amount
                        })
                        invoice["total"] += amount
            current_date += timedelta(days=1)
        
        return invoice