{% extends "base.html" %}

{% block title %}Daily Summary Report - Sandwich Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-calendar-day"></i> Daily Summary Report</h1>
    <div>
        <input type="date" class="form-control" id="date-picker" 
               value="{{ report_date.strftime('%Y-%m-%d') }}" 
               onchange="window.location.href='{{ url_for('daily_summary_report') }}?date=' + this.value">
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info">
            <h5 class="alert-heading">{{ report_date.strftime('%A, %B %d, %Y') }}</h5>
            <p class="mb-0">Complete operational summary for the selected date.</p>
        </div>
    </div>
</div>

<!-- Key Metrics -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3>{{ plan.planned_quantities.values() | sum if plan else 0 }}</h3>
                <p class="mb-0">Planned Production</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <h3>{{ plan.produced_quantities.values() | sum if plan else 0 }}</h3>
                <p class="mb-0">Actual Production</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                {% set total_sold = 0 %}
                {% if plan %}
                    {% for sandwich in sandwiches %}
                        {% set _ = total_sold.__add__(plan.get_total_sold(sandwich)) %}
                    {% endfor %}
                {% endif %}
                <h3>{{ total_sold }}</h3>
                <p class="mb-0">Total Sold</p>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h3>${{ "%.2f"|format(plan.calculate_daily_revenue() if plan else 0) }}</h3>
                <p class="mb-0">Revenue</p>
            </div>
        </div>
    </div>
</div>

<!-- Production Details -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-gear"></i> Production Summary</h5>
            </div>
            <div class="card-body">
                {% if plan and sandwiches %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Sandwich</th>
                                    <th>Planned</th>
                                    <th>Produced</th>
                                    <th>Variance</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sandwich in sandwiches %}
                                    {% set planned = plan.planned_quantities.get(sandwich, 0) %}
                                    {% set produced = plan.produced_quantities.get(sandwich, 0) %}
                                    {% set variance = produced - planned %}
                                    <tr>
                                        <td>{{ sandwich.name }}</td>
                                        <td>{{ planned }}</td>
                                        <td>{{ produced }}</td>
                                        <td>
                                            <span class="badge {% if variance >= 0 %}bg-success{% else %}bg-danger{% endif %}">
                                                {{ "+" if variance > 0 else "" }}{{ variance }}
                                            </span>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No production data available for this date.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-currency-dollar"></i> Financial Summary</h5>
            </div>
            <div class="card-body">
                {% if plan %}
                    <div class="row">
                        <div class="col-6">
                            <div class="metric-card bg-light">
                                <div class="metric-value">${{ "%.2f"|format(plan.calculate_daily_revenue()) }}</div>
                                <div class="metric-label">Revenue</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="metric-card bg-light">
                                <div class="metric-value">${{ "%.2f"|format(plan.calculate_daily_costs()) }}</div>
                                <div class="metric-label">Costs</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="metric-card bg-light">
                                <div class="metric-value">${{ "%.2f"|format(plan.calculate_daily_profit()) }}</div>
                                <div class="metric-label">Profit</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="metric-card bg-light">
                                <div class="metric-value">
                                    {% if plan.calculate_daily_revenue() > 0 %}
                                        {{ "%.1f"|format((plan.calculate_daily_profit() / plan.calculate_daily_revenue()) * 100) }}%
                                    {% else %}
                                        0%
                                    {% endif %}
                                </div>
                                <div class="metric-label">Profit Margin</div>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <p class="text-muted">No financial data available for this date.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Shop Performance -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-shop"></i> Shop Performance</h5>
            </div>
            <div class="card-body">
                {% if plan and shops %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Shop</th>
                                    <th>Delivered</th>
                                    <th>Returned</th>
                                    <th>Net Sold</th>
                                    <th>Revenue</th>
                                    <th>Return Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for shop in shops %}
                                    {% set shop_delivered = 0 %}
                                    {% set shop_returned = 0 %}
                                    {% set shop_revenue = 0 %}
                                    
                                    {% for sandwich in sandwiches %}
                                        {% set delivered = plan.shop_deliveries.get(shop, {}).get(sandwich, 0) %}
                                        {% set returned = plan.shop_returns.get(shop, {}).get(sandwich, 0) %}
                                        {% set sold = delivered - returned %}
                                        {% set _ = shop_delivered.__add__(delivered) %}
                                        {% set _ = shop_returned.__add__(returned) %}
                                        {% set _ = shop_revenue.__add__(sold * sandwich.selling_price) %}
                                    {% endfor %}
                                    
                                    <tr>
                                        <td><strong>{{ shop.name }}</strong></td>
                                        <td>{{ shop_delivered }}</td>
                                        <td>{{ shop_returned }}</td>
                                        <td>{{ shop_delivered - shop_returned }}</td>
                                        <td>${{ "%.2f"|format(shop_revenue) }}</td>
                                        <td>
                                            {% if shop_delivered > 0 %}
                                                <span class="badge {% if (shop_returned / shop_delivered) < 0.1 %}bg-success{% elif (shop_returned / shop_delivered) < 0.2 %}bg-warning{% else %}bg-danger{% endif %}">
                                                    {{ "%.1f"|format((shop_returned / shop_delivered) * 100) }}%
                                                </span>
                                            {% else %}
                                                <span class="badge bg-secondary">0%</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No shop performance data available for this date.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-gear"></i> Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-flex gap-2 flex-wrap">
                    <a href="{{ url_for('daily_plan', date=report_date.strftime('%Y-%m-%d')) }}" class="btn btn-outline-primary">
                        <i class="bi bi-calendar-check"></i> View Daily Plan
                    </a>
                    <a href="{{ url_for('production', date=report_date.strftime('%Y-%m-%d')) }}" class="btn btn-outline-success">
                        <i class="bi bi-gear"></i> Production
                    </a>
                    <a href="{{ url_for('deliveries', date=report_date.strftime('%Y-%m-%d')) }}" class="btn btn-outline-info">
                        <i class="bi bi-truck"></i> Deliveries
                    </a>
                    <a href="{{ url_for('returns', date=report_date.strftime('%Y-%m-%d')) }}" class="btn btn-outline-warning">
                        <i class="bi bi-arrow-return-left"></i> Returns
                    </a>
                    <button onclick="window.print()" class="btn btn-outline-secondary">
                        <i class="bi bi-printer"></i> Print Report
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
