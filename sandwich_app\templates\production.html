{% extends "base.html" %}

{% block title %}Production - Sandwich Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-gear"></i> Production</h1>
    <div>
        <input type="date" class="form-control" id="date-picker" 
               value="{{ plan_date.strftime('%Y-%m-%d') }}" 
               onchange="window.location.href='{{ url_for('production') }}?date=' + this.value">
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>Production for {{ plan_date.strftime('%B %d, %Y') }}</h5>
            </div>
            <div class="card-body">
                {% if sandwiches %}
                    <form method="POST">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Sandwich</th>
                                        <th>Planned</th>
                                        <th>Produced</th>
                                        <th>New Production</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for sandwich in sandwiches %}
                                        {% set planned = plan.planned_quantities.get(sandwich, 0) %}
                                        {% set produced = plan.produced_quantities.get(sandwich, 0) %}
                                        <tr>
                                            <td>
                                                <strong>{{ sandwich.name }}</strong><br>
                                                <small class="text-muted">Cost: ${{ "%.2f"|format(sandwich.calculate_cost()) }}</small>
                                            </td>
                                            <td>{{ planned }}</td>
                                            <td>{{ produced }}</td>
                                            <td>
                                                <input type="number" class="form-control" 
                                                       name="produced_{{ loop.index0 }}" 
                                                       value="{{ produced }}" 
                                                       min="0" step="1">
                                            </td>
                                            <td>
                                                {% if produced >= planned %}
                                                    <span class="badge bg-success">Complete</span>
                                                {% elif produced > 0 %}
                                                    <span class="badge bg-warning">Partial</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">Not Started</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Production
                            </button>
                        </div>
                    </form>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-inbox display-4 text-muted"></i>
                        <h4 class="text-muted">No sandwiches available</h4>
                        <p class="text-muted">Add sandwich types first to track production.</p>
                        <a href="{{ url_for('add_sandwich') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Add Sandwich
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6>Production Summary</h6>
            </div>
            <div class="card-body">
                {% set total_planned = plan.planned_quantities.values() | sum %}
                {% set total_produced = plan.produced_quantities.values() | sum %}
                
                <div class="metric-card bg-light">
                    <div class="metric-value">{{ total_planned }}</div>
                    <div class="metric-label">Total Planned</div>
                </div>
                <div class="metric-card bg-light">
                    <div class="metric-value">{{ total_produced }}</div>
                    <div class="metric-label">Total Produced</div>
                </div>
                <div class="metric-card bg-light">
                    <div class="metric-value">
                        {% if total_planned > 0 %}
                            {{ "%.1f"|format((total_produced / total_planned) * 100) }}%
                        {% else %}
                            0%
                        {% endif %}
                    </div>
                    <div class="metric-label">Completion Rate</div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6>Ingredient Requirements</h6>
            </div>
            <div class="card-body">
                {% set ingredient_totals = {} %}
                {% for sandwich in sandwiches %}
                    {% set planned_qty = plan.planned_quantities.get(sandwich, 0) %}
                    {% if planned_qty > 0 %}
                        {% for ingredient, qty_per_sandwich in sandwich.recipe.items() %}
                            {% set total_needed = planned_qty * qty_per_sandwich %}
                            {% if ingredient.name in ingredient_totals %}
                                {% set _ = ingredient_totals.update({ingredient.name: ingredient_totals[ingredient.name] + total_needed}) %}
                            {% else %}
                                {% set _ = ingredient_totals.update({ingredient.name: total_needed}) %}
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                {% endfor %}
                
                {% if ingredient_totals %}
                    <div class="list-group list-group-flush">
                        {% for ingredient_name, total_qty in ingredient_totals.items() %}
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <span>{{ ingredient_name }}</span>
                                <span class="badge bg-primary">{{ "%.2f"|format(total_qty) }}</span>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">No ingredients required</p>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6>Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('daily_plan', date=plan_date.strftime('%Y-%m-%d')) }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-calendar-check"></i> View Plan
                    </a>
                    <a href="{{ url_for('deliveries', date=plan_date.strftime('%Y-%m-%d')) }}" class="btn btn-outline-success btn-sm">
                        <i class="bi bi-truck"></i> Manage Deliveries
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
