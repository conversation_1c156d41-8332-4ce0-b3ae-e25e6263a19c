<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Sandwich Management System{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        /* Enhanced Navigation Styles */
        .navbar-nav .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 0.375rem;
            font-weight: 600;
        }

        .navbar-nav .dropdown-menu {
            border: none;
            border-radius: 0.5rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            margin-top: 0.5rem;
        }

        .navbar-nav .dropdown-item.active {
            background-color: var(--bs-primary);
            color: white;
            font-weight: 600;
        }

        .navbar-nav .dropdown-item:hover {
            background-color: var(--bs-light);
            transform: translateX(5px);
            transition: all 0.2s ease;
        }

        .navbar-nav .dropdown-item.active:hover {
            background-color: var(--bs-primary);
            transform: translateX(5px);
        }

        .dropdown-header {
            font-weight: 600;
            color: var(--bs-primary);
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .breadcrumb {
            background-color: transparent;
            font-size: 0.9rem;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            font-weight: bold;
            color: var(--bs-secondary);
        }

        .breadcrumb-item.active {
            font-weight: 600;
            color: var(--bs-primary);
        }

        /* Quick Action Button Styles */
        .btn-outline-light:hover {
            background-color: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        /* Original Styles */
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8f9fa;
        }
        .main-content {
            min-height: calc(100vh - 56px);
        }
        .metric-card {
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .alert-dismissible .btn-close {
            position: absolute;
            top: 0;
            right: 0;
            z-index: 2;
            padding: 1.25rem 1rem;
        }

        /* Additional Enhanced Styles */
        .card-hover {
            transition: transform 0.2s;
        }
        .card-hover:hover {
            transform: translateY(-2px);
        }
        .table-responsive {
            border-radius: 0.375rem;
        }
        .btn-group-sm > .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        .nav-link {
            font-weight: 500;
            transition: all 0.2s ease;
        }
        .dropdown-item {
            font-weight: 400;
            transition: all 0.2s ease;
        }
        .card-title {
            color: var(--bs-primary);
            font-weight: 600;
        }
        .text-muted {
            font-size: 0.9rem;
        }
        .badge {
            font-weight: 500;
        }
        .btn {
            font-weight: 500;
            transition: all 0.2s ease;
        }
        .form-label {
            font-weight: 500;
            color: var(--bs-dark);
        }
        .table th {
            font-weight: 600;
            background-color: var(--bs-light);
            border-bottom: 2px solid var(--bs-border-color);
        }
        .list-group-item {
            border-left: 3px solid transparent;
        }
        .list-group-item.active {
            border-left-color: var(--bs-primary);
        }
        .progress {
            height: 0.5rem;
        }
        .toast {
            border-left: 4px solid var(--bs-success);
        }
        .modal-header {
            background-color: var(--bs-light);
            border-bottom: 2px solid var(--bs-border-color);
        }
        .modal-title {
            color: var(--bs-primary);
            font-weight: 600;
        }
        .input-group-text {
            background-color: var(--bs-light);
            border-color: var(--bs-border-color);
            font-weight: 500;
        }
        .form-control:focus, .form-select:focus {
            border-color: var(--bs-primary);
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        .btn-outline-primary:hover {
            transform: translateY(-1px);
        }
        .btn-outline-secondary:hover {
            transform: translateY(-1px);
        }
        .btn-outline-success:hover {
            transform: translateY(-1px);
        }
        .btn-outline-danger:hover {
            transform: translateY(-1px);
        }
        .btn-outline-warning:hover {
            transform: translateY(-1px);
        }
        .btn-outline-info:hover {
            transform: translateY(-1px);
        }
        .shadow-sm {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
        }
        .border-start-primary {
            border-left: 4px solid var(--bs-primary) !important;
        }
        .border-start-success {
            border-left: 4px solid var(--bs-success) !important;
        }
        .border-start-warning {
            border-left: 4px solid var(--bs-warning) !important;
        }
        .border-start-danger {
            border-left: 4px solid var(--bs-danger) !important;
        }
        .border-start-info {
            border-left: 4px solid var(--bs-info) !important;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Enhanced Navigation Header -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="{{ url_for('home') }}">
                <i class="bi bi-shop fs-4"></i> Sandwich Management System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Main Navigation Tabs -->
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link px-3 {{ 'active' if request.endpoint == 'home' else '' }}" href="{{ url_for('home') }}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle px-3 {{ 'active' if request.endpoint in ['sandwiches', 'add_sandwich', 'edit_sandwich', 'ingredients', 'add_ingredient', 'edit_ingredient', 'suppliers', 'add_supplier', 'edit_supplier', 'shops', 'add_shop', 'edit_shop'] else '' }}" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-gear-fill"></i> Management
                        </a>
                        <ul class="dropdown-menu shadow">
                            <li><h6 class="dropdown-header"><i class="bi bi-collection"></i> Products</h6></li>
                            <li><a class="dropdown-item {{ 'active' if request.endpoint in ['sandwiches', 'add_sandwich', 'edit_sandwich'] else '' }}" href="{{ url_for('sandwiches') }}">
                                <i class="bi bi-card-list"></i> Sandwiches
                            </a></li>
                            <li><a class="dropdown-item {{ 'active' if request.endpoint in ['ingredients', 'add_ingredient', 'edit_ingredient'] else '' }}" href="{{ url_for('ingredients') }}">
                                <i class="bi bi-egg"></i> Ingredients
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header"><i class="bi bi-building"></i> Partners</h6></li>
                            <li><a class="dropdown-item {{ 'active' if request.endpoint in ['suppliers', 'add_supplier', 'edit_supplier'] else '' }}" href="{{ url_for('suppliers') }}">
                                <i class="bi bi-truck"></i> Suppliers
                            </a></li>
                            <li><a class="dropdown-item {{ 'active' if request.endpoint in ['shops', 'add_shop', 'edit_shop'] else '' }}" href="{{ url_for('shops') }}">
                                <i class="bi bi-shop"></i> Shops
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle px-3 {{ 'active' if request.endpoint in ['daily_plan', 'production', 'deliveries', 'returns'] else '' }}" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-calendar-check"></i> Operations
                        </a>
                        <ul class="dropdown-menu shadow">
                            <li><a class="dropdown-item {{ 'active' if request.endpoint == 'daily_plan' else '' }}" href="{{ url_for('daily_plan') }}">
                                <i class="bi bi-calendar-plus"></i> Daily Planning
                            </a></li>
                            <li><a class="dropdown-item {{ 'active' if request.endpoint == 'production' else '' }}" href="{{ url_for('production') }}">
                                <i class="bi bi-gear"></i> Production
                            </a></li>
                            <li><a class="dropdown-item {{ 'active' if request.endpoint == 'deliveries' else '' }}" href="{{ url_for('deliveries') }}">
                                <i class="bi bi-truck"></i> Deliveries
                            </a></li>
                            <li><a class="dropdown-item {{ 'active' if request.endpoint == 'returns' else '' }}" href="{{ url_for('returns') }}">
                                <i class="bi bi-arrow-return-left"></i> Returns
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link px-3 {{ 'active' if request.endpoint in ['orders', 'add_order', 'view_order'] else '' }}" href="{{ url_for('orders') }}">
                            <i class="bi bi-receipt-cutoff"></i> Orders
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle px-3 {{ 'active' if request.endpoint in ['reports', 'daily_summary_report', 'shop_invoice_report', 'import_data'] else '' }}" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-graph-up"></i> Reports
                        </a>
                        <ul class="dropdown-menu shadow">
                            <li><a class="dropdown-item {{ 'active' if request.endpoint == 'reports' else '' }}" href="{{ url_for('reports') }}">
                                <i class="bi bi-file-text"></i> All Reports
                            </a></li>
                            <li><a class="dropdown-item {{ 'active' if request.endpoint == 'daily_summary_report' else '' }}" href="{{ url_for('daily_summary_report') }}">
                                <i class="bi bi-calendar-day"></i> Daily Summary
                            </a></li>
                            <li><a class="dropdown-item {{ 'active' if request.endpoint == 'shop_invoice_report' else '' }}" href="{{ url_for('shop_invoice_report') }}">
                                <i class="bi bi-receipt"></i> Shop Invoice
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item {{ 'active' if request.endpoint == 'import_data' else '' }}" href="{{ url_for('import_data') }}">
                                <i class="bi bi-upload"></i> Import Data
                            </a></li>
                        </ul>
                    </li>
                </ul>

                <!-- Quick Action Buttons -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle btn btn-outline-light btn-sm me-2" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-plus-circle"></i> Quick Add
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow">
                            <li><a class="dropdown-item" href="{{ url_for('add_order') }}">
                                <i class="bi bi-receipt-cutoff text-primary"></i> New Order
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('add_sandwich') }}">
                                <i class="bi bi-card-list text-success"></i> New Sandwich
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('add_ingredient') }}">
                                <i class="bi bi-egg text-warning"></i> New Ingredient
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('add_supplier') }}">
                                <i class="bi bi-truck text-info"></i> New Supplier
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('add_shop') }}">
                                <i class="bi bi-shop text-secondary"></i> New Shop
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-gear"></i> Admin
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow">
                            <li><a class="dropdown-item" href="{{ url_for('admin_save_data') }}">
                                <i class="bi bi-save text-success"></i> Save Data
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin_backup_data') }}">
                                <i class="bi bi-shield-check text-primary"></i> Backup Data
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('import_data') }}">
                                <i class="bi bi-upload text-info"></i> Import Data
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb Navigation -->
    <nav aria-label="breadcrumb" class="bg-light border-bottom">
        <div class="container-fluid">
            <ol class="breadcrumb mb-0 py-2">
                <li class="breadcrumb-item">
                    <a href="{{ url_for('home') }}" class="text-decoration-none">
                        <i class="bi bi-house"></i> Home
                    </a>
                </li>
                {% if request.endpoint != 'home' %}
                    {% set page_info = get_page_info() %}
                    {% if page_info.section %}
                        <li class="breadcrumb-item">
                            <a href="{{ page_info.section_url }}" class="text-decoration-none">{{ page_info.section }}</a>
                        </li>
                    {% endif %}
                    <li class="breadcrumb-item active" aria-current="page">{{ page_info.page }}</li>
                {% endif %}
            </ol>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <main class="col-12 main-content">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="mt-3">
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}

                <!-- Page Content -->
                <div class="mt-4">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
