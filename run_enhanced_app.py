#!/usr/bin/env python3
"""
Enhanced Sandwich Management System Web Application
Run this file to start the web application with enhanced navigation
"""

import os
from sandwich_app.web_app import SandwichWebApp

def main():
    try:
        print("🥪 Starting Enhanced Sandwich Management System...")

        # Set environment variables for sample data
        os.environ['LOAD_SAMPLE_DATA'] = 'true'

        print("📦 Importing modules...")
        # Create and run the web application
        web_app = SandwichWebApp()

        print("📊 Loading sample data...")
        print("🌐 Web interface will be available at: http://localhost:5000")
        print("🎯 Features:")
        print("   • Enhanced tab-based navigation")
        print("   • Breadcrumb navigation")
        print("   • Quick action buttons")
        print("   • Responsive design")
        print("   • Active page highlighting")
        print("\n🚀 Starting server...")

        web_app.flask_app.run(debug=True, port=5000, host='0.0.0.0')
    except KeyboardInterrupt:
        print("\n👋 Shutting down Enhanced Sandwich Management System...")
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
