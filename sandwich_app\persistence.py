"""
Data persistence module for the Sandwich Management System.
Provides JSON-based storage for application data.
"""

import json
import os
from datetime import date, datetime
from typing import Dict, Any


class DataPersistence:
    """Handles saving and loading application data to/from JSON files."""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = data_dir
        self.ensure_data_directory()
    
    def ensure_data_directory(self):
        """Create data directory if it doesn't exist."""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def save_app_data(self, app):
        """Save all application data to JSON files."""
        try:
            # Save suppliers
            suppliers_data = []
            for supplier in app.suppliers:
                suppliers_data.append({
                    'name': supplier.name,
                    'contact_info': supplier.contact_info
                })
            self._save_json('suppliers.json', suppliers_data)
            
            # Save ingredients
            ingredients_data = []
            for ingredient in app.ingredients:
                # Find supplier index
                supplier_index = app.suppliers.index(ingredient.supplier)
                ingredients_data.append({
                    'name': ingredient.name,
                    'unit': ingredient.unit,
                    'price_per_unit': ingredient.price_per_unit,
                    'supplier_index': supplier_index
                })
            self._save_json('ingredients.json', ingredients_data)
            
            # Save sandwich types
            sandwiches_data = []
            for sandwich in app.sandwich_types:
                recipe_data = {}
                for ingredient, quantity in sandwich.recipe.items():
                    ingredient_index = app.ingredients.index(ingredient)
                    recipe_data[str(ingredient_index)] = quantity
                
                sandwiches_data.append({
                    'name': sandwich.name,
                    'description': sandwich.description,
                    'selling_price': sandwich.selling_price,
                    'recipe': recipe_data
                })
            self._save_json('sandwiches.json', sandwiches_data)
            
            # Save shops
            shops_data = []
            for shop in app.shops:
                shops_data.append({
                    'name': shop.name,
                    'location': shop.location
                })
            self._save_json('shops.json', shops_data)
            
            # Save daily plans
            plans_data = {}
            for plan_date, plan in app.daily_plans.items():
                date_str = plan_date.isoformat()
                
                # Convert planned quantities
                planned_quantities = {}
                for sandwich, quantity in plan.planned_quantities.items():
                    sandwich_index = app.sandwich_types.index(sandwich)
                    planned_quantities[str(sandwich_index)] = quantity
                
                # Convert produced quantities
                produced_quantities = {}
                for sandwich, quantity in plan.produced_quantities.items():
                    sandwich_index = app.sandwich_types.index(sandwich)
                    produced_quantities[str(sandwich_index)] = quantity
                
                # Convert shop deliveries
                shop_deliveries = {}
                for shop, deliveries in plan.shop_deliveries.items():
                    shop_index = app.shops.index(shop)
                    shop_deliveries[str(shop_index)] = {}
                    for sandwich, quantity in deliveries.items():
                        sandwich_index = app.sandwich_types.index(sandwich)
                        shop_deliveries[str(shop_index)][str(sandwich_index)] = quantity
                
                # Convert shop returns
                shop_returns = {}
                for shop, returns in plan.shop_returns.items():
                    shop_index = app.shops.index(shop)
                    shop_returns[str(shop_index)] = {}
                    for sandwich, quantity in returns.items():
                        sandwich_index = app.sandwich_types.index(sandwich)
                        shop_returns[str(shop_index)][str(sandwich_index)] = quantity
                
                plans_data[date_str] = {
                    'planned_quantities': planned_quantities,
                    'produced_quantities': produced_quantities,
                    'shop_deliveries': shop_deliveries,
                    'shop_returns': shop_returns
                }
            
            self._save_json('daily_plans.json', plans_data)
            
            return True
            
        except Exception as e:
            print(f"Error saving data: {e}")
            return False
    
    def load_app_data(self, app):
        """Load application data from JSON files."""
        try:
            # Load suppliers
            suppliers_data = self._load_json('suppliers.json', [])
            for supplier_data in suppliers_data:
                app.add_supplier(supplier_data['name'], supplier_data['contact_info'])
            
            # Load ingredients
            ingredients_data = self._load_json('ingredients.json', [])
            for ingredient_data in ingredients_data:
                supplier = app.suppliers[ingredient_data['supplier_index']]
                app.add_ingredient(
                    ingredient_data['name'],
                    ingredient_data['unit'],
                    supplier,
                    ingredient_data['price_per_unit']
                )
            
            # Load sandwich types
            sandwiches_data = self._load_json('sandwiches.json', [])
            for sandwich_data in sandwiches_data:
                sandwich = app.add_sandwich_type(
                    sandwich_data['name'],
                    sandwich_data['description']
                )
                sandwich.set_price(sandwich_data['selling_price'])
                
                # Add recipe
                for ingredient_index_str, quantity in sandwich_data['recipe'].items():
                    ingredient_index = int(ingredient_index_str)
                    ingredient = app.ingredients[ingredient_index]
                    sandwich.add_ingredient(ingredient, quantity)
            
            # Load shops
            shops_data = self._load_json('shops.json', [])
            for shop_data in shops_data:
                app.add_shop(shop_data['name'], shop_data['location'])
            
            # Load daily plans
            plans_data = self._load_json('daily_plans.json', {})
            for date_str, plan_data in plans_data.items():
                plan_date = datetime.fromisoformat(date_str).date()
                plan = app.create_daily_plan(plan_date)
                
                # Load planned quantities
                for sandwich_index_str, quantity in plan_data['planned_quantities'].items():
                    sandwich_index = int(sandwich_index_str)
                    sandwich = app.sandwich_types[sandwich_index]
                    plan.plan_quantity(sandwich, quantity)
                
                # Load produced quantities
                for sandwich_index_str, quantity in plan_data['produced_quantities'].items():
                    sandwich_index = int(sandwich_index_str)
                    sandwich = app.sandwich_types[sandwich_index]
                    plan.record_production(sandwich, quantity)
                
                # Load shop deliveries
                for shop_index_str, deliveries in plan_data['shop_deliveries'].items():
                    shop_index = int(shop_index_str)
                    shop = app.shops[shop_index]
                    for sandwich_index_str, quantity in deliveries.items():
                        sandwich_index = int(sandwich_index_str)
                        sandwich = app.sandwich_types[sandwich_index]
                        plan.record_delivery(shop, sandwich, quantity)
                
                # Load shop returns
                for shop_index_str, returns in plan_data['shop_returns'].items():
                    shop_index = int(shop_index_str)
                    shop = app.shops[shop_index]
                    for sandwich_index_str, quantity in returns.items():
                        sandwich_index = int(sandwich_index_str)
                        sandwich = app.sandwich_types[sandwich_index]
                        plan.record_returns(shop, sandwich, quantity)
            
            return True
            
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
    
    def _save_json(self, filename: str, data: Any):
        """Save data to a JSON file."""
        filepath = os.path.join(self.data_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def _load_json(self, filename: str, default: Any = None):
        """Load data from a JSON file."""
        filepath = os.path.join(self.data_dir, filename)
        if not os.path.exists(filepath):
            return default
        
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def backup_data(self, backup_name: str = None):
        """Create a backup of all data files."""
        if backup_name is None:
            backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        backup_dir = os.path.join(self.data_dir, 'backups', backup_name)
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        # Copy all JSON files to backup directory
        import shutil
        for filename in os.listdir(self.data_dir):
            if filename.endswith('.json'):
                src = os.path.join(self.data_dir, filename)
                dst = os.path.join(backup_dir, filename)
                shutil.copy2(src, dst)
        
        return backup_dir
