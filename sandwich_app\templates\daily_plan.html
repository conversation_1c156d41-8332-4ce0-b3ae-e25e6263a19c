{% extends "base.html" %}

{% block title %}Daily Planning - Sandwich Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="bi bi-calendar-plus text-primary"></i> Daily Planning
                </h2>
                <div class="btn-group">
                    <button class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Create Plan
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="bi bi-calendar"></i> View Calendar
                    </button>
                </div>
            </div>

            <!-- Date Selector -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-4">
                            <label class="form-label">Planning Date</label>
                            <input type="date" class="form-control" value="{{ date.today() }}">
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-outline-primary">
                                <i class="bi bi-search"></i> Load Plan
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Planning Overview -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card shadow-sm border-start-primary">
                        <div class="card-body">
                            <h6 class="card-title text-primary">Total Orders</h6>
                            <h3 class="mb-0">{{ total_orders or 0 }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card shadow-sm border-start-success">
                        <div class="card-body">
                            <h6 class="card-title text-success">Sandwiches to Make</h6>
                            <h3 class="mb-0">{{ total_sandwiches or 0 }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card shadow-sm border-start-warning">
                        <div class="card-body">
                            <h6 class="card-title text-warning">Shops to Deliver</h6>
                            <h3 class="mb-0">{{ total_shops or 0 }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card shadow-sm border-start-info">
                        <div class="card-body">
                            <h6 class="card-title text-info">Estimated Revenue</h6>
                            <h3 class="mb-0">${{ "%.2f"|format(estimated_revenue or 0) }}</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Production Plan -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-gear"></i> Production Plan
                    </h5>
                </div>
                <div class="card-body">
                    {% if production_plan %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Sandwich Type</th>
                                    <th>Quantity Needed</th>
                                    <th>Ingredients Required</th>
                                    <th>Estimated Time</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in production_plan %}
                                <tr>
                                    <td>
                                        <strong>{{ item.sandwich_name }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ item.quantity }}</span>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ item.ingredients_summary }}</small>
                                    </td>
                                    <td>{{ item.estimated_time }} min</td>
                                    <td>
                                        <span class="badge bg-warning">Planned</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-calendar-plus text-muted" style="font-size: 4rem;"></i>
                        <h4 class="text-muted mt-3">No Production Plan</h4>
                        <p class="text-muted">Create a daily plan to see production requirements.</p>
                        <button class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Create Today's Plan
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
