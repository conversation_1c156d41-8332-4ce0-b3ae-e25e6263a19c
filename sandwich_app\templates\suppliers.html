{% extends "base.html" %}

{% block title %}Suppliers - Sandwich Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-truck"></i> Suppliers</h1>
    <a href="{{ url_for('add_supplier') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> Add New Supplier
    </a>
</div>

{% if suppliers %}
    <div class="row">
        {% for supplier in suppliers %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">{{ supplier.name }}</h5>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('edit_supplier', supplier_id=loop.index0) }}">
                                    <i class="bi bi-pencil"></i> Edit
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="confirmDelete('{{ supplier.name }}', '{{ url_for('delete_supplier', supplier_id=loop.index0) }}')">
                                    <i class="bi bi-trash"></i> Delete
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if supplier.contact_info %}
                            <p class="card-text">
                                <i class="bi bi-envelope"></i> {{ supplier.contact_info }}
                            </p>
                        {% else %}
                            <p class="text-muted"><small>No contact information</small></p>
                        {% endif %}
                        
                        <!-- Show ingredients supplied -->
                        {% set supplier_ingredients = [] %}
                        {% for ingredient in ingredients %}
                            {% if ingredient.supplier == supplier %}
                                {% set _ = supplier_ingredients.append(ingredient) %}
                            {% endif %}
                        {% endfor %}
                        
                        {% if supplier_ingredients %}
                            <h6 class="mt-3">Supplies:</h6>
                            <div class="d-flex flex-wrap gap-1">
                                {% for ingredient in supplier_ingredients %}
                                    <span class="badge bg-secondary">{{ ingredient.name }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-muted mt-3"><small>No ingredients assigned</small></p>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="bi bi-truck display-1 text-muted"></i>
        <h3 class="text-muted">No suppliers yet</h3>
        <p class="text-muted">Start by adding your first supplier.</p>
        <a href="{{ url_for('add_supplier') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Add First Supplier
        </a>
    </div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete supplier <strong id="deleteSupplierName"></strong>?</p>
                <p class="text-warning"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">Delete</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function confirmDelete(supplierName, deleteUrl) {
        document.getElementById('deleteSupplierName').textContent = supplierName;
        document.getElementById('confirmDeleteBtn').href = deleteUrl;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }
</script>
{% endblock %}
