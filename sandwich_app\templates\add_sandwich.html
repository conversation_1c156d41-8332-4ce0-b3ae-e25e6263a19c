{% extends "base.html" %}

{% block title %}Add Sandwich - Sandwich Management System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3><i class="bi bi-plus-circle"></i> Add New Sandwich</h3>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">Sandwich Name *</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="price" class="form-label">Selling Price ($) *</label>
                        <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                    </div>
                    
                    <h5>Ingredients</h5>
                    <div id="ingredients-section">
                        {% if ingredients %}
                            {% for ingredient in ingredients %}
                                <div class="row mb-2">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input ingredient-checkbox" type="checkbox" 
                                                   value="{{ loop.index0 }}" id="ingredient_{{ loop.index0 }}"
                                                   name="ingredient_id">
                                            <label class="form-check-label" for="ingredient_{{ loop.index0 }}">
                                                {{ ingredient.name }} ({{ ingredient.unit }})
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <input type="number" class="form-control quantity-input" 
                                               name="quantity" step="0.01" min="0" 
                                               placeholder="Quantity" disabled>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted">No ingredients available. <a href="{{ url_for('add_ingredient') }}">Add ingredients first</a>.</p>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('sandwiches') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Sandwiches
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Add Sandwich
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Enable/disable quantity inputs based on checkbox selection
    document.addEventListener('DOMContentLoaded', function() {
        const checkboxes = document.querySelectorAll('.ingredient-checkbox');
        const quantityInputs = document.querySelectorAll('.quantity-input');
        
        checkboxes.forEach((checkbox, index) => {
            checkbox.addEventListener('change', function() {
                quantityInputs[index].disabled = !this.checked;
                if (!this.checked) {
                    quantityInputs[index].value = '';
                }
            });
        });
    });
</script>
{% endblock %}
