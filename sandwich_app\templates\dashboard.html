<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sandwich Business Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .metric-card {
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
        }
        .metric-label {
            font-size: 1rem;
            color: #666;
        }
        .profit { background-color: #d4edda; }
        .revenue { background-color: #d1ecf1; }
        .production { background-color: #fff3cd; }
        .returns { background-color: #f8d7da; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">Sandwich Business Dashboard</h1>
        
        <div class="row">
            <div class="col-md-3">
                <div class="metric-card production">
                    <div class="metric-value">{{ stats.total_sandwiches_produced }}</div>
                    <div class="metric-label">Sandwiches Produced</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card revenue">
                    <div class="metric-value">${{ "%.2f"|format(stats.revenue) }}</div>
                    <div class="metric-label">Total Revenue</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card profit">
                    <div class="metric-value">${{ "%.2f"|format(stats.profit) }}</div>
                    <div class="metric-label">Total Profit</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card returns">
                    <div class="metric-value">{{ "%.1f"|format(stats.return_rate) }}%</div>
                    <div class="metric-label">Return Rate</div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        Daily Sales Trend
                    </div>
                    <div class="card-body">
                        <img id="sales-trend" class="img-fluid" alt="Sales Trend">
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        Sandwich Popularity
                    </div>
                    <div class="card-body">
                        <img id="popularity" class="img-fluid" alt="Sandwich Popularity">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        Shop Performance
                    </div>
                    <div class="card-body">
                        <img id="shops" class="img-fluid" alt="Shop Performance">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        Top Performers
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <h5>Top Sandwich</h5>
                                <p class="lead">{{ stats.top_sandwich.name }}</p>
                                <p>{{ stats.top_sandwich.sold }} sold</p>
                            </div>
                            <div class="col-6">
                                <h5>Top Shop</h5>
                                <p class="lead">{{ stats.top_shop.name }}</p>
                                <p>{{ stats.top_shop.sold }} sold</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        Period Summary
                    </div>
                    <div class="card-body">
                        <p><strong>Period:</strong> {{ stats.period }}</p>
                        <p><strong>Total Produced:</strong> {{ stats.total_sandwiches_produced }} sandwiches</p>
                        <p><strong>Total Sold:</strong> {{ stats.total_sandwiches_sold }} sandwiches</p>
                        <p><strong>Profit Margin:</strong> {{ "%.1f"|format(stats.profit/stats.revenue*100 if stats.revenue else 0) }}%</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Load chart images when page loads
        window.onload = function() {
            fetch('/api/charts/sales_trend')
                .then(response => response.json())
                .then(data => {
                    if (data.type === 'svg') {
                        document.getElementById('sales-trend').src = 'data:image/svg+xml;base64,' + data.image;
                    } else {
                        document.getElementById('sales-trend').src = 'data:image/png;base64,' + data.image;
                    }
                });

            fetch('/api/charts/popularity')
                .then(response => response.json())
                .then(data => {
                    if (data.type === 'svg') {
                        document.getElementById('popularity').src = 'data:image/svg+xml;base64,' + data.image;
                    } else {
                        document.getElementById('popularity').src = 'data:image/png;base64,' + data.image;
                    }
                });

            fetch('/api/charts/shops')
                .then(response => response.json())
                .then(data => {
                    if (data.type === 'svg') {
                        document.getElementById('shops').src = 'data:image/svg+xml;base64,' + data.image;
                    } else {
                        document.getElementById('shops').src = 'data:image/png;base64,' + data.image;
                    }
                });
        };
    </script>
</body>
</html>