{% extends "base.html" %}

{% block title %}Add Ingredient - Sandwich Management System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3><i class="bi bi-plus-circle"></i> Add New Ingredient</h3>
            </div>
            <div class="card-body">
                {% if suppliers %}
                    <form method="POST">
                        <div class="mb-3">
                            <label for="name" class="form-label">Ingredient Name *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="form-text">Enter the name of the ingredient (e.g., "Bread", "Ham", "Cheese").</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="unit" class="form-label">Unit of Measurement *</label>
                            <input type="text" class="form-control" id="unit" name="unit" required placeholder="e.g., kg, loaf, jar, head">
                            <div class="form-text">How this ingredient is measured (kg, grams, pieces, etc.).</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="price" class="form-label">Price per Unit ($) *</label>
                            <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                            <div class="form-text">Cost per unit from the supplier.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="supplier_id" class="form-label">Supplier *</label>
                            <select class="form-select" id="supplier_id" name="supplier_id" required>
                                <option value="">Select a supplier...</option>
                                {% for supplier in suppliers %}
                                    <option value="{{ loop.index0 }}">{{ supplier.name }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">Choose which supplier provides this ingredient.</div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('ingredients') }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Back to Ingredients
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Add Ingredient
                            </button>
                        </div>
                    </form>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-exclamation-triangle display-4 text-warning"></i>
                        <h4 class="text-warning">No Suppliers Available</h4>
                        <p class="text-muted">You need to add at least one supplier before you can add ingredients.</p>
                        <a href="{{ url_for('add_supplier') }}" class="btn btn-warning">
                            <i class="bi bi-plus-circle"></i> Add Supplier First
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
