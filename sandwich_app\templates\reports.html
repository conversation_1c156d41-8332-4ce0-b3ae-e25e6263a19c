{% extends "base.html" %}

{% block title %}Reports - Sandwich Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-file-text"></i> Reports</h1>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5><i class="bi bi-calendar-day"></i> Daily Summary Report</h5>
            </div>
            <div class="card-body">
                <p class="card-text">View comprehensive daily performance including production, deliveries, returns, and financial metrics.</p>
                
                <form action="{{ url_for('daily_summary_report') }}" method="GET" class="mb-3">
                    <div class="row">
                        <div class="col-8">
                            <input type="date" class="form-control" name="date" value="{{ today.strftime('%Y-%m-%d') }}">
                        </div>
                        <div class="col-4">
                            <button type="submit" class="btn btn-primary w-100">View Report</button>
                        </div>
                    </div>
                </form>
                
                <div class="d-grid">
                    <a href="{{ url_for('daily_summary_report') }}" class="btn btn-outline-primary">
                        View Today's Report
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5><i class="bi bi-receipt"></i> Shop Invoice Report</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Generate invoices for specific shops showing deliveries, returns, and amounts due.</p>
                
                <form action="{{ url_for('shop_invoice_report') }}" method="GET" class="mb-3">
                    <div class="mb-2">
                        <select class="form-select" name="shop_id" required>
                            <option value="">Select a shop...</option>
                            {% for shop in shops %}
                                <option value="{{ loop.index0 }}">{{ shop.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <input type="date" class="form-control" name="start_date" value="{{ (today - timedelta(days=7)).strftime('%Y-%m-%d') }}">
                        </div>
                        <div class="col-6">
                            <input type="date" class="form-control" name="end_date" value="{{ today.strftime('%Y-%m-%d') }}">
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary w-100 mt-2">Generate Invoice</button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5><i class="bi bi-graph-up"></i> Performance Analytics</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Analyze trends, compare performance across shops, and identify top-performing products.</p>
                
                <div class="d-grid gap-2">
                    <a href="{{ url_for('reports') }}?type=trends" class="btn btn-outline-info">
                        <i class="bi bi-graph-up"></i> Sales Trends
                    </a>
                    <a href="{{ url_for('reports') }}?type=shop_comparison" class="btn btn-outline-info">
                        <i class="bi bi-bar-chart"></i> Shop Comparison
                    </a>
                    <a href="{{ url_for('reports') }}?type=product_analysis" class="btn btn-outline-info">
                        <i class="bi bi-pie-chart"></i> Product Analysis
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5><i class="bi bi-download"></i> Data Export</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Export data for external analysis or backup purposes.</p>
                
                <div class="d-grid gap-2">
                    <a href="{{ url_for('export_data') }}?type=daily_plans" class="btn btn-outline-success">
                        <i class="bi bi-file-earmark-spreadsheet"></i> Export Daily Plans
                    </a>
                    <a href="{{ url_for('export_data') }}?type=deliveries" class="btn btn-outline-success">
                        <i class="bi bi-file-earmark-spreadsheet"></i> Export Deliveries
                    </a>
                    <a href="{{ url_for('export_data') }}?type=financial" class="btn btn-outline-success">
                        <i class="bi bi-file-earmark-spreadsheet"></i> Export Financial Data
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

{% if request.args.get('type') %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>
                        {% if request.args.get('type') == 'trends' %}
                            <i class="bi bi-graph-up"></i> Sales Trends Analysis
                        {% elif request.args.get('type') == 'shop_comparison' %}
                            <i class="bi bi-bar-chart"></i> Shop Performance Comparison
                        {% elif request.args.get('type') == 'product_analysis' %}
                            <i class="bi bi-pie-chart"></i> Product Performance Analysis
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if request.args.get('type') == 'trends' %}
                        <div class="row">
                            {% for i in range(7) %}
                                {% set report_date = today - timedelta(days=i) %}
                                {% set plan = daily_plans.get(report_date) %}
                                <div class="col-md-3 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h6>{{ report_date.strftime('%m/%d') }}</h6>
                                            <p class="mb-1">
                                                <strong>{{ plan.produced_quantities.values() | sum if plan else 0 }}</strong><br>
                                                <small class="text-muted">Produced</small>
                                            </p>
                                            <p class="mb-0">
                                                <strong>${{ "%.0f"|format(plan.calculate_daily_revenue() if plan else 0) }}</strong><br>
                                                <small class="text-muted">Revenue</small>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% elif request.args.get('type') == 'shop_comparison' %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Shop</th>
                                        <th>Total Delivered (7d)</th>
                                        <th>Total Returned (7d)</th>
                                        <th>Net Sold (7d)</th>
                                        <th>Revenue (7d)</th>
                                        <th>Return Rate</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for shop in shops %}
                                        {% set shop_stats = {"delivered": 0, "returned": 0, "revenue": 0} %}
                                        {% for plan_date, plan in daily_plans.items() if plan_date >= (today - timedelta(days=7)) %}
                                            {% for sandwich in sandwiches %}
                                                {% set delivered = plan.shop_deliveries.get(shop, {}).get(sandwich, 0) %}
                                                {% set returned = plan.shop_returns.get(shop, {}).get(sandwich, 0) %}
                                                {% set sold = delivered - returned %}
                                                {% set _ = shop_stats.update({"delivered": shop_stats.delivered + delivered}) %}
                                                {% set _ = shop_stats.update({"returned": shop_stats.returned + returned}) %}
                                                {% set _ = shop_stats.update({"revenue": shop_stats.revenue + (sold * sandwich.selling_price)}) %}
                                            {% endfor %}
                                        {% endfor %}
                                        <tr>
                                            <td><strong>{{ shop.name }}</strong></td>
                                            <td>{{ shop_stats.delivered }}</td>
                                            <td>{{ shop_stats.returned }}</td>
                                            <td>{{ shop_stats.delivered - shop_stats.returned }}</td>
                                            <td>${{ "%.2f"|format(shop_stats.revenue) }}</td>
                                            <td>
                                                {% if shop_stats.delivered > 0 %}
                                                    {{ "%.1f"|format((shop_stats.returned / shop_stats.delivered) * 100) }}%
                                                {% else %}
                                                    0%
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% elif request.args.get('type') == 'product_analysis' %}
                        <div class="row">
                            {% for sandwich in sandwiches %}
                                {% set sandwich_stats = {"sold": 0, "revenue": 0} %}
                                {% for plan_date, plan in daily_plans.items() if plan_date >= (today - timedelta(days=7)) %}
                                    {% set sold = plan.get_total_sold(sandwich) %}
                                    {% set _ = sandwich_stats.update({"sold": sandwich_stats.sold + sold}) %}
                                    {% set _ = sandwich_stats.update({"revenue": sandwich_stats.revenue + (sold * sandwich.selling_price)}) %}
                                {% endfor %}
                                <div class="col-md-4 mb-3">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h6>{{ sandwich.name }}</h6>
                                            <p class="mb-1">
                                                <strong>{{ sandwich_stats.sold }}</strong><br>
                                                <small class="text-muted">Sold (7d)</small>
                                            </p>
                                            <p class="mb-1">
                                                <strong>${{ "%.2f"|format(sandwich_stats.revenue) }}</strong><br>
                                                <small class="text-muted">Revenue (7d)</small>
                                            </p>
                                            <p class="mb-0">
                                                <strong>{{ "%.1f"|format(sandwich.calculate_profit_margin()) }}%</strong><br>
                                                <small class="text-muted">Profit Margin</small>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}
