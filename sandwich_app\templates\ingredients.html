{% extends "base.html" %}

{% block title %}Ingredients - Sandwich Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-basket"></i> Ingredients</h1>
    <a href="{{ url_for('add_ingredient') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> Add New Ingredient
    </a>
</div>

{% if ingredients %}
    <div class="row">
        {% for ingredient in ingredients %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">{{ ingredient.name }}</h5>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('edit_ingredient', ingredient_id=loop.index0) }}">
                                    <i class="bi bi-pencil"></i> Edit
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="confirmDelete('{{ ingredient.name }}', '{{ url_for('delete_ingredient', ingredient_id=loop.index0) }}')">
                                    <i class="bi bi-trash"></i> Delete
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">Unit:</small>
                                <p class="mb-2">{{ ingredient.unit }}</p>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Price per unit:</small>
                                <p class="mb-2">${{ "%.2f"|format(ingredient.price_per_unit) }}</p>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">Supplier:</small>
                            <p class="mb-0">
                                <span class="badge bg-info">{{ ingredient.supplier.name }}</span>
                            </p>
                        </div>
                        
                        <!-- Show which sandwiches use this ingredient -->
                        {% set using_sandwiches = [] %}
                        {% for sandwich in sandwiches %}
                            {% if ingredient in sandwich.recipe %}
                                {% set _ = using_sandwiches.append(sandwich) %}
                            {% endif %}
                        {% endfor %}
                        
                        {% if using_sandwiches %}
                            <div class="mt-3">
                                <small class="text-muted">Used in:</small>
                                <div class="d-flex flex-wrap gap-1 mt-1">
                                    {% for sandwich in using_sandwiches %}
                                        <span class="badge bg-secondary">{{ sandwich.name }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="bi bi-basket display-1 text-muted"></i>
        <h3 class="text-muted">No ingredients yet</h3>
        <p class="text-muted">Start by adding your first ingredient.</p>
        {% if suppliers %}
            <a href="{{ url_for('add_ingredient') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Add First Ingredient
            </a>
        {% else %}
            <p class="text-warning">You need to add suppliers first before adding ingredients.</p>
            <a href="{{ url_for('add_supplier') }}" class="btn btn-warning">
                <i class="bi bi-plus-circle"></i> Add Supplier First
            </a>
        {% endif %}
    </div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete ingredient <strong id="deleteIngredientName"></strong>?</p>
                <p class="text-warning"><small>This will also remove it from any sandwich recipes.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">Delete</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function confirmDelete(ingredientName, deleteUrl) {
        document.getElementById('deleteIngredientName').textContent = ingredientName;
        document.getElementById('confirmDeleteBtn').href = deleteUrl;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }
</script>
{% endblock %}
