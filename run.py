#!/usr/bin/env python3
"""
Sandwich App - Main Entry Point

This script starts the Flask web application for the Sandwich Management System.
"""

import os
import sys
from sandwich_app.web_app import SandwichWebApp

def main():
    """Main entry point for the application"""
    
    # Set default environment variables if not already set
    if 'SECRET_KEY' not in os.environ:
        print("Warning: SECRET_KEY not set. Using development key.")
        os.environ['SECRET_KEY'] = 'dev-key-change-in-production'
    
    # Load sample data in development
    if 'LOAD_SAMPLE_DATA' not in os.environ:
        os.environ['LOAD_SAMPLE_DATA'] = 'true'
    
    # Get configuration from environment
    debug = os.environ.get('FLASK_DEBUG', 'true').lower() == 'true'
    port = int(os.environ.get('PORT', 5000))
    host = os.environ.get('HOST', '127.0.0.1')
    
    try:
        # Create and run the web application
        print("Starting Sandwich Management System...")
        print(f"Debug mode: {debug}")
        print(f"Server: http://{host}:{port}")
        print("Press Ctrl+C to stop the server")
        
        web_app = SandwichWebApp()
        web_app.run(debug=debug, port=port, host=host)
        
    except KeyboardInterrupt:
        print("\nShutting down gracefully...")
        sys.exit(0)
    except Exception as e:
        print(f"Error starting application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
