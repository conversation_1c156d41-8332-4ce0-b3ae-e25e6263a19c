{% extends "base.html" %}

{% block title %}Shops - Sandwich Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="bi bi-shop text-secondary"></i> Shop Management
                </h2>
                <a href="{{ url_for('add_shop') }}" class="btn btn-secondary">
                    <i class="bi bi-plus-circle"></i> Add New Shop
                </a>
            </div>

            {% if shops %}
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Location</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for shop in shops %}
                                <tr>
                                    <td>
                                        <strong>{{ shop.name }}</strong>
                                    </td>
                                    <td>{{ shop.location or '-' }}</td>
                                    <td>
                                        <span class="badge bg-success">Active</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('edit_shop', shop_id=loop.index0) }}" 
                                               class="btn btn-outline-primary">
                                                <i class="bi bi-pencil"></i> Edit
                                            </a>
                                            <button class="btn btn-outline-danger" 
                                                    onclick="confirmDelete('{{ shop.name }}', '{{ loop.index0 }}')">
                                                <i class="bi bi-trash"></i> Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="bi bi-shop text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">No Shops Found</h4>
                    <p class="text-muted">Get started by adding your first shop.</p>
                    <a href="{{ url_for('add_shop') }}" class="btn btn-secondary">
                        <i class="bi bi-plus-circle"></i> Add First Shop
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function confirmDelete(name, id) {
    if (confirm(`Are you sure you want to delete "${name}"?`)) {
        // Add delete functionality here
        window.location.href = `/shops/delete/${id}`;
    }
}
</script>
{% endblock %}
