{% extends "base.html" %}

{% block title %}Returns - Sandwich Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-arrow-return-left"></i> Returns</h1>
    <div class="d-flex gap-2">
        <select class="form-select" id="shop-selector" onchange="updateShop()">
            {% for shop in shops %}
                <option value="{{ loop.index0 }}" {% if shop == selected_shop %}selected{% endif %}>{{ shop.name }}</option>
            {% endfor %}
        </select>
        <input type="date" class="form-control" id="date-picker" 
               value="{{ plan_date.strftime('%Y-%m-%d') }}" 
               onchange="updateDate()">
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5>Returns from {{ selected_shop.name if selected_shop else 'No Shop Selected' }} - {{ plan_date.strftime('%B %d, %Y') }}</h5>
            </div>
            <div class="card-body">
                {% if selected_shop and sandwiches %}
                    <form method="POST">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Sandwich</th>
                                        <th>Delivered</th>
                                        <th>Current Returns</th>
                                        <th>New Returns</th>
                                        <th>Net Sold</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for sandwich in sandwiches %}
                                        {% set delivered = plan.shop_deliveries.get(selected_shop, {}).get(sandwich, 0) %}
                                        {% set returned = plan.shop_returns.get(selected_shop, {}).get(sandwich, 0) %}
                                        <tr>
                                            <td>
                                                <strong>{{ sandwich.name }}</strong><br>
                                                <small class="text-muted">${{ "%.2f"|format(sandwich.selling_price) }}</small>
                                            </td>
                                            <td>{{ delivered }}</td>
                                            <td>{{ returned }}</td>
                                            <td>
                                                <input type="number" class="form-control return-input" 
                                                       name="returns_{{ loop.index0 }}" 
                                                       value="{{ returned }}" 
                                                       min="0" max="{{ delivered }}" step="1"
                                                       data-delivered="{{ delivered }}"
                                                       data-index="{{ loop.index0 }}">
                                            </td>
                                            <td class="net-sold" id="net-{{ loop.index0 }}">
                                                {{ delivered - returned }}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Returns
                            </button>
                        </div>
                    </form>
                {% elif not selected_shop %}
                    <div class="text-center py-4">
                        <i class="bi bi-shop display-4 text-muted"></i>
                        <h4 class="text-muted">No shop selected</h4>
                        <p class="text-muted">Please select a shop to manage returns.</p>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-inbox display-4 text-muted"></i>
                        <h4 class="text-muted">No sandwiches available</h4>
                        <p class="text-muted">Add sandwich types first to track returns.</p>
                        <a href="{{ url_for('add_sandwich') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Add Sandwich
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        {% if selected_shop %}
            <div class="card">
                <div class="card-header">
                    <h6>{{ selected_shop.name }} Summary</h6>
                </div>
                <div class="card-body">
                    {% set total_delivered = 0 %}
                    {% set total_returned = 0 %}
                    {% set total_revenue = 0 %}
                    
                    {% for sandwich in sandwiches %}
                        {% set delivered = plan.shop_deliveries.get(selected_shop, {}).get(sandwich, 0) %}
                        {% set returned = plan.shop_returns.get(selected_shop, {}).get(sandwich, 0) %}
                        {% set sold = delivered - returned %}
                        {% set _ = total_delivered.__add__(delivered) %}
                        {% set _ = total_returned.__add__(returned) %}
                        {% set _ = total_revenue.__add__(sold * sandwich.selling_price) %}
                    {% endfor %}
                    
                    <div class="metric-card bg-light">
                        <div class="metric-value">{{ total_delivered }}</div>
                        <div class="metric-label">Total Delivered</div>
                    </div>
                    <div class="metric-card bg-light">
                        <div class="metric-value">{{ total_returned }}</div>
                        <div class="metric-label">Total Returned</div>
                    </div>
                    <div class="metric-card bg-light">
                        <div class="metric-value">{{ total_delivered - total_returned }}</div>
                        <div class="metric-label">Net Sold</div>
                    </div>
                    <div class="metric-card bg-light">
                        <div class="metric-value">${{ "%.2f"|format(total_revenue) }}</div>
                        <div class="metric-label">Revenue</div>
                    </div>
                    
                    {% if total_delivered > 0 %}
                        <div class="metric-card bg-light">
                            <div class="metric-value">{{ "%.1f"|format((total_returned / total_delivered) * 100) }}%</div>
                            <div class="metric-label">Return Rate</div>
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endif %}
        
        <div class="card mt-3">
            <div class="card-header">
                <h6>Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('deliveries', date=plan_date.strftime('%Y-%m-%d'), shop_id=selected_shop_id if selected_shop else 0) }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-truck"></i> Manage Deliveries
                    </a>
                    {% if selected_shop %}
                        <a href="{{ url_for('shop_invoice_report', shop_id=selected_shop_id) }}" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-file-text"></i> View Invoice
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function updateShop() {
        const shopId = document.getElementById('shop-selector').value;
        const date = document.getElementById('date-picker').value;
        window.location.href = `{{ url_for('returns') }}?shop_id=${shopId}&date=${date}`;
    }
    
    function updateDate() {
        const shopId = document.getElementById('shop-selector').value;
        const date = document.getElementById('date-picker').value;
        window.location.href = `{{ url_for('returns') }}?shop_id=${shopId}&date=${date}`;
    }
    
    // Update net sold calculations when return quantities change
    document.addEventListener('DOMContentLoaded', function() {
        const returnInputs = document.querySelectorAll('.return-input');
        
        returnInputs.forEach(input => {
            input.addEventListener('input', function() {
                const delivered = parseInt(this.dataset.delivered);
                const returned = parseInt(this.value) || 0;
                const index = this.dataset.index;
                const netSold = delivered - returned;
                
                document.getElementById(`net-${index}`).textContent = netSold;
                
                // Validate that returns don't exceed deliveries
                if (returned > delivered) {
                    this.setCustomValidity('Returns cannot exceed deliveries');
                } else {
                    this.setCustomValidity('');
                }
            });
        });
    });
</script>
{% endblock %}
