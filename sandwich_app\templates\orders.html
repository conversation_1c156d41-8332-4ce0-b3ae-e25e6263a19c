{% extends "base.html" %}

{% block title %}Orders - Sandwich Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="bi bi-receipt-cutoff text-primary"></i> Order Management
                </h2>
                <a href="{{ url_for('add_order') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> New Order
                </a>
            </div>

            <!-- Order Filters -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Date Range</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Shop</label>
                            <select class="form-select" id="shopFilter">
                                <option value="">All Shops</option>
                                {% for shop in shops %}
                                <option value="{{ loop.index0 }}">{{ shop.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <button class="btn btn-outline-primary w-100">
                                <i class="bi bi-funnel"></i> Filter
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {% if orders %}
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Order #</th>
                                    <th>Shop</th>
                                    <th>Date</th>
                                    <th>Items</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr>
                                    <td>
                                        <strong>#{{ order.id or loop.index }}</strong>
                                    </td>
                                    <td>{{ order.shop.name if order.shop else '-' }}</td>
                                    <td>{{ order.date.strftime('%Y-%m-%d') if order.date else '-' }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ order.items|length }} items</span>
                                    </td>
                                    <td>
                                        <span class="text-success fw-bold">${{ "%.2f"|format(order.total or 0) }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">Pending</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('view_order', order_id=loop.index0) }}" 
                                               class="btn btn-outline-primary">
                                                <i class="bi bi-eye"></i> View
                                            </a>
                                            <a href="{{ url_for('edit_order', order_id=loop.index0) }}" 
                                               class="btn btn-outline-secondary">
                                                <i class="bi bi-pencil"></i> Edit
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="bi bi-receipt-cutoff text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">No Orders Found</h4>
                    <p class="text-muted">Get started by creating your first order.</p>
                    <a href="{{ url_for('add_order') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Create First Order
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
